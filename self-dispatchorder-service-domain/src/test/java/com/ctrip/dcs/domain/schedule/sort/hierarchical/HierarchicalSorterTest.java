package com.ctrip.dcs.domain.schedule.sort.hierarchical;

import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.grade.Grade;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * 分层排序器测试类
 * 
 * 测试新的分层排序系统的核心功能：
 * 1. 基本排序功能
 * 2. 等级计算
 * 3. 类别分数计算
 * 4. 降级处理
 * 5. 配置管理
 * 
 * <AUTHOR>
 */
class HierarchicalSorterTest {

    @Mock
    private ConfigService configService;

    @Mock
    private SortContext sortContext;

    private HierarchicalSortFactory factory;
    private HierarchicalSorter hierarchicalSorter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 设置配置服务的默认返回值
        setupConfigServiceMocks();
        
        // 创建工厂和排序器
        factory = new HierarchicalSortFactory(configService);
        hierarchicalSorter = factory.getHierarchicalSorter();
    }

    /**
     * 测试基本排序功能
     */
    @Test
    void testBasicSorting() {
        // 准备测试数据
        List<SortModel> models = createTestSortModels();
        
        // 执行排序
        List<SortModel> sortedModels = hierarchicalSorter.sort(models, sortContext);
        
        // 验证结果
        assertNotNull(sortedModels);
        assertEquals(3, sortedModels.size());
        
        // 验证每个模型都有分层评分
        for (SortModel model : sortedModels) {
            assertNotNull(model.getHierarchicalScore());
            assertNotNull(model.getHierarchicalScore().getOverallGrade());
        }
        
        // 验证排序顺序 (第一个应该是最高等级)
        HierarchicalScore firstScore = sortedModels.get(0).getHierarchicalScore();
        HierarchicalScore lastScore = sortedModels.get(sortedModels.size() - 1).getHierarchicalScore();
        
        assertTrue(firstScore.getOverallGrade().getNumericValue() >= 
                  lastScore.getOverallGrade().getNumericValue());
    }

    /**
     * 测试等级计算
     */
    @Test
    void testGradeCalculation() {
        // 测试分数到等级的转换
        assertEquals(Grade.A, Grade.fromScore(90.0));
        assertEquals(Grade.B, Grade.fromScore(75.0));
        assertEquals(Grade.C, Grade.fromScore(60.0));
        assertEquals(Grade.D, Grade.fromScore(30.0));
        
        // 测试等级比较
        assertTrue(Grade.A.isHigherThan(Grade.B));
        assertTrue(Grade.B.isHigherThan(Grade.C));
        assertTrue(Grade.C.isHigherThan(Grade.D));
        
        assertFalse(Grade.D.isHigherThan(Grade.A));
    }

    /**
     * 测试配置管理
     */
    @Test
    void testConfigurationManagement() {
        HierarchicalSortConfig config = factory.getConfig();
        
        // 测试类别权重
        Map<String, Double> categoryWeights = config.getAllCategoryWeights();
        assertNotNull(categoryWeights);
        assertEquals(4, categoryWeights.size());
        
        // 验证权重总和为1.0 (允许小误差)
        double totalWeight = categoryWeights.values().stream()
            .mapToDouble(Double::doubleValue)
            .sum();
        assertEquals(1.0, totalWeight, 0.01);
        
        // 测试子项权重
        double etaWeight = config.getSubItemWeight("TIME_SPACE_EFFICIENCY", "ETA");
        assertTrue(etaWeight > 0.0);
        assertTrue(etaWeight <= 1.0);
    }

    /**
     * 测试监控功能
     */
    @Test
    void testMonitoring() {
        // 执行一次排序以产生监控数据
        List<SortModel> models = createTestSortModels();
        hierarchicalSorter.sort(models, sortContext);
        
        // 检查监控统计
        Map<String, Object> stats = factory.getMonitoringStats();
        assertNotNull(stats);
        
        // 验证基本统计信息存在
        assertTrue(stats.containsKey("total_sorting_count"));
        assertTrue(stats.containsKey("success_sorting_count"));
        assertTrue(stats.containsKey("health_score"));
        
        // 验证健康度分数
        double healthScore = factory.getSystemHealthScore();
        assertTrue(healthScore >= 0.0);
        assertTrue(healthScore <= 100.0);
    }

    /**
     * 测试降级处理
     */
    @Test
    void testFallbackHandling() {
        // 创建一个会导致异常的排序上下文
        SortContext faultyContext = createFaultyContext();
        List<SortModel> models = createTestSortModels();
        
        // 执行排序 (应该触发降级处理)
        List<SortModel> sortedModels = hierarchicalSorter.sort(models, faultyContext);
        
        // 验证降级处理正常工作
        assertNotNull(sortedModels);
        assertEquals(models.size(), sortedModels.size());
        
        // 检查监控中是否记录了降级
        Map<String, Object> stats = factory.getMonitoringStats();
        // 注意：实际的降级统计可能需要根据具体实现调整
    }

    /**
     * 测试空列表处理
     */
    @Test
    void testEmptyListHandling() {
        List<SortModel> emptyModels = Arrays.asList();
        List<SortModel> result = hierarchicalSorter.sort(emptyModels, sortContext);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试系统诊断
     */
    @Test
    void testSystemDiagnostics() {
        Map<String, Object> diagnostics = factory.performDiagnostics();
        
        assertNotNull(diagnostics);
        assertTrue(diagnostics.containsKey("hierarchical_sort_enabled"));
        assertTrue(diagnostics.containsKey("config_valid"));
        assertTrue(diagnostics.containsKey("system_health_score"));
        assertTrue(diagnostics.containsKey("diagnosis_status"));
        
        assertEquals("SUCCESS", diagnostics.get("diagnosis_status"));
    }

    // ========== 辅助方法 ==========

    /**
     * 设置配置服务的模拟返回值
     */
    private void setupConfigServiceMocks() {
        // 启用分层排序
        when(configService.getBoolean("hierarchical.sort.enabled", true)).thenReturn(true);
        
        // 类别权重配置
        when(configService.getDouble("hierarchical.sort.category.weight.time_space_efficiency", 0.45)).thenReturn(0.45);
        when(configService.getDouble("hierarchical.sort.category.weight.service_quality", 0.25)).thenReturn(0.25);
        when(configService.getDouble("hierarchical.sort.category.weight.order_matching", 0.20)).thenReturn(0.20);
        when(configService.getDouble("hierarchical.sort.category.weight.global_efficiency", 0.10)).thenReturn(0.10);
        
        // 子项权重配置 (使用默认值)
        when(configService.getDouble(anyString(), eq(0.25))).thenReturn(0.25);
        when(configService.getDouble(anyString(), eq(0.20))).thenReturn(0.20);
        
        // 策略配置
        when(configService.getString("hierarchical.sort.strategy.overall_grade", "WEIGHTED_AVERAGE")).thenReturn("WEIGHTED_AVERAGE");
        
        // 降级阈值
        when(configService.getDouble("hierarchical.sort.fallback_threshold", 0.1)).thenReturn(0.1);
    }

    /**
     * 创建测试用的排序模型
     */
    private List<SortModel> createTestSortModels() {
        // 创建司机1 - 高分司机
        DriverVO driver1 = new DriverVO();
        driver1.setDriverId(1001L);
        
        DspOrderVO order1 = new DspOrderVO();
        order1.setDspOrderId("ORDER_001");
        order1.setCityId(1);
        
        DspModelVO model1 = new DspModelVO();
        model1.setDriver(driver1);
        model1.setOrder(order1);
        
        SortModel sortModel1 = new SortModel(model1);
        
        // 创建司机2 - 中等分司机
        DriverVO driver2 = new DriverVO();
        driver2.setDriverId(1002L);
        
        DspOrderVO order2 = new DspOrderVO();
        order2.setDspOrderId("ORDER_002");
        order2.setCityId(1);
        
        DspModelVO model2 = new DspModelVO();
        model2.setDriver(driver2);
        model2.setOrder(order2);
        
        SortModel sortModel2 = new SortModel(model2);
        
        // 创建司机3 - 低分司机
        DriverVO driver3 = new DriverVO();
        driver3.setDriverId(1003L);
        
        DspOrderVO order3 = new DspOrderVO();
        order3.setDspOrderId("ORDER_003");
        order3.setCityId(1);
        
        DspModelVO model3 = new DspModelVO();
        model3.setDriver(driver3);
        model3.setOrder(order3);
        
        SortModel sortModel3 = new SortModel(model3);
        
        return Arrays.asList(sortModel1, sortModel2, sortModel3);
    }

    /**
     * 创建会导致异常的排序上下文 (用于测试降级)
     */
    private SortContext createFaultyContext() {
        // 这里可以创建一个会在某些操作中抛出异常的上下文
        // 用于测试降级处理机制
        return sortContext; // 暂时返回正常上下文
    }
}
