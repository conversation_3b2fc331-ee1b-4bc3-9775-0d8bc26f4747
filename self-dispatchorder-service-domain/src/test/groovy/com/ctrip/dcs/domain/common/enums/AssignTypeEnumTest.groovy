package com.ctrip.dcs.domain.common.enums


import spock.lang.Specification

class AssignTypeEnumTest extends Specification {


    def "test 1 "() {
        when:
        def code = AssignTypeEnum.SYS.getCode()
        def des = AssignTypeEnum.SYS.getDes()
        AssignTypeEnum anEnum = AssignTypeEnum.fromCode(code)
        String name = AssignTypeEnum.des(code)

        then:
        code > 0 == true
        des.length() > 0 == true
        Objects.isNull(anEnum) == false
        name.length() > 0 == true
    }

    def "test 2"() {
        when:
        def code = AssignTypeEnum.BOSS.getCode()
        def des = AssignTypeEnum.BOSS.getDes()

        then:
        code > 0 == true
        des.length() > 0 == true
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme