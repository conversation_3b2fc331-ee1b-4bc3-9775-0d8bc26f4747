package com.ctrip.dcs.domain.util

import com.ctrip.dcs.domain.common.util.MetricsUtil
import spock.lang.Specification

class MetricsUtilTest extends Specification {

    def "test record Value"() {
        when:
        MetricsUtil.recordValue("name")

        then:
        true
    }

    def "test record Value 2"() {
        when:
        MetricsUtil.recordValue("name", 0l)

        then:
        true
    }

    def "test record Value 3"() {
        when:
        long d = 17L
        MetricsUtil.recordValue("name", d)

        then:
        d> 0 ==true
    }

    def "test record Value 4"() {
        when:
        int i = 99
        MetricsUtil.gaugeValue("name", i)

        then:
        i> 0 ==true
    }

    def "test record Value 5"() {
        when:
        long d = 17L
        MetricsUtil.recordTime("name", d)

        then:
        d> 0 ==true
    }

    def "test record Value 6"() {
        when:
        int i = 99
        MetricsUtil.gaugeValue("name", i, "1", "1")

        then:
        i> 0 ==true
    }

    def "test record Value 7"() {
        when:
        int i = 99
        MetricsUtil.gaugeValue("name", i, "1", "2", "3")

        then:
        i> 0 ==true
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme