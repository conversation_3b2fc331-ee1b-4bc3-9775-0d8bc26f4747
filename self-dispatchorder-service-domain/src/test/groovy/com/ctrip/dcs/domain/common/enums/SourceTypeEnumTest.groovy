package com.ctrip.dcs.domain.common.enums


import spock.lang.Specification

class SourceTypeEnumTest extends Specification {


    def "test 1"() {
        when:
        def code = SourceTypeEnum.order.getCode()
        def des = SourceTypeEnum.order.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 2"() {
        when:
        def code = SourceTypeEnum.distribution.getCode()
        def des = SourceTypeEnum.distribution.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 3"() {
        when:
        def code = SourceTypeEnum.other.getCode()
        def des = SourceTypeEnum.other.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 4"() {
        when:
        def code = SourceTypeEnum.customer.getCode()
        def des = SourceTypeEnum.customer.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 5"() {
        when:
        def code = SourceTypeEnum.customerService.getCode()
        def des = SourceTypeEnum.customerService.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 6"() {
        when:
        def code = SourceTypeEnum.driver.getCode()
        def des = SourceTypeEnum.driver.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme