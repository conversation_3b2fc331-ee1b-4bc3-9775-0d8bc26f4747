package com.ctrip.dcs.domain.common.enums

import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/4/29 10:34
 */
class CategoryCodeEnumTest extends Specification {


    @Unroll
    def "isPointToPointTest"() {
        given:

        when:
        def result = CategoryCodeEnum.isPointToPoint(type)

        then:
        result == expectedResult


        where:
        type   || expectedResult
        "point_to_point" || true
        "taxi_prebook"   || false

    }
}
