package com.ctrip.dcs.domain.common.enums


import spock.lang.Specification

class CancelReasonIdEnumTest extends Specification {


    def "test 1"() {
        when:
        def code = CancelReasonIdEnum.UNKNOWN_CANCEL.getCode()
        def des = CancelReasonIdEnum.UNKNOWN_CANCEL.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 2"() {
        when:
        def code = CancelReasonIdEnum.TAKEN.getCode()
        def des = CancelReasonIdEnum.TAKEN.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 3"() {
        when:
        def code = CancelReasonIdEnum.REDISPATCH.getCode()
        def des = CancelReasonIdEnum.REDISPATCH.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 4"() {
        when:
        def code = CancelReasonIdEnum.USER_BREAK_POINT.getCode()
        def des = CancelReasonIdEnum.USER_BREAK_POINT.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 5"() {
        when:
        def code = CancelReasonIdEnum.DISPATCH_CONFIRM.getCode()
        def des = CancelReasonIdEnum.DISPATCH_CONFIRM.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 6"() {
        when:
        def code = CancelReasonIdEnum.OTA_CANCEL.getCode()
        def des = CancelReasonIdEnum.OTA_CANCEL.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 7"() {
        when:
        def code = CancelReasonIdEnum.CHANGE_DRIVER_CANCEL.getCode()
        def des = CancelReasonIdEnum.CHANGE_DRIVER_CANCEL.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 8"() {
        when:
        def code = CancelReasonIdEnum.NO_CAR_CHECK_CANCEL.getCode()
        def des = CancelReasonIdEnum.NO_CAR_CHECK_CANCEL.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 9"() {
        when:
        def code = CancelReasonIdEnum.CANCEL_SUPPLY_API.getCode()
        def des = CancelReasonIdEnum.CANCEL_SUPPLY_API.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme