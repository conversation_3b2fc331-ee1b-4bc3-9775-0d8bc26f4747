package com.ctrip.dcs.domain.common.enums

import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2025/4/29 16:28
 */
class VehStatusEnumTest extends Specification {

    @Unroll
    def "test values"() {
        given:
        when:
        Integer code = VehStatusEnum.OFFLINE.getCode();
        String text = VehStatusEnum.OFFLINE.getText();
        then:
        Assert.assertTrue(Objects.equals(code, 3) && Objects.equals(text, "Offline"));
    }
}
