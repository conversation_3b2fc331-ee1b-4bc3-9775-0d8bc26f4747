package com.ctrip.dcs.domain.common.enums


import spock.lang.Specification

class LogTypeEnumTest extends Specification {


    def "test 1"() {
        when:
        def code = LogTypeEnum.system.getCode()
        def des = LogTypeEnum.system.getDesc()

        then:
        code > 0 == false
        des.length() > 0 == true
    }

    def "test 2"() {
        when:
        def code = LogTypeEnum.artificial.getCode()
        def des = LogTypeEnum.artificial.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme