package com.ctrip.dcs.domain.common.util

import spock.lang.Specification
import spock.lang.Unroll

class DigitalUtilTest extends Specification {
    def testObj = new DigitalUtil()

    @Unroll
    def "getIntegerTest"() {
        given: "设定相关方法入参"
        when:
        def result = DigitalUtil.getInteger(str)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        str    || expectedResult
        "1234" || 1234
        ""     || null
    }

    @Unroll
    def "getLongTest"() {
        given: "设定相关方法入参"
        when:
        def result = DigitalUtil.getLong(str)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        str   || expectedResult
        "1234" || 1234L
        "" || null
    }

    @Unroll
    def "getStringOfListTest"() {
        given: "设定相关方法入参"
        when:
        def result = DigitalUtil.getStringOfList(strings)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        strings    || expectedResult
        ["1234","1234"] || "1234,1234"
    }

    @Unroll
    def "getListOfStringTest"() {
        given: "设定相关方法入参"
        when:
        def result = DigitalUtil.getListOfString(str)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        str   || expectedResult
        "1234,1234" || ["1234","1234"]
    }

    @Unroll
    def "getStringOfListLongTest"() {
        given: "设定相关方法入参"
        when:
        def result = DigitalUtil.getStringOfListLong(longList)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        longList || expectedResult
        [1L,2L]     || "1,2"
    }
}
