package com.ctrip.dcs.domain.schedule.check.item.impl


import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.OrderExtendAttributeCodeEnum
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderExtendAttributeVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.domain.schedule.value.carconfig.SameCarTypeDspValueVO
import org.mockito.InjectMocks
import org.mockito.Mock
import spock.lang.Specification
import spock.lang.Unroll

class SameCarTypeDspCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car

    @Mock
    DriverVO driverInfo
    @InjectMocks
    SameCarTypeDspCheck sameCarTypeDspCheck = new SameCarTypeDspCheck()

    @Unroll
    def "test check"() {
        given:
        CheckModel checkModel = new CheckModel(model: new DspModelVO(order: orderVo, car: new CarVO(carTypeId: 1), driver: new DriverVO(car: new CarVO(carTypeId: drivercarTypeId))))
        CheckContext context = Mock()
        context.querySameCarTypeDspValue(_) >> sameCarTypeDspValueVO
        context.getSubSku() >> new SubSkuVO(dspType: dspType)

        when:
        CheckCode result = sameCarTypeDspCheck.checkSameCarType(checkModel, context)

        then:
        result == r

        where:
        drivercarTypeId | carTypeId | dspType               | sameCarTypeDspValueVO               | orderVo                                                                                                                                                                                                                             || r
        null            | null      | null                  | null                                | new DspOrderVO(carTypeId: 1)                                                                                                                                                                                                        || CheckCode.DRV_CAR_TYPE_NOT_MATCH
        null            | null      | DspType.DELAY_ASSIGN  | null                                | new DspOrderVO(carTypeId: 1)                                                                                                                                                                                                        || CheckCode.PASS
        null            | null      | DspType.SYSTEM_ASSIGN | null                                | null                                                                                                                                                                                                                                || CheckCode.DRV_CAR_TYPE_NOT_MATCH
        null            | null      | DspType.SYSTEM_ASSIGN | null                                | new DspOrderVO(carTypeId: 1, orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 1)])                                             || CheckCode.PASS
        null            | null      | DspType.SYSTEM_ASSIGN | null                                | new DspOrderVO(createTime: null, carTypeId: 1, orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])                           || CheckCode.DRV_CAR_TYPE_NOT_MATCH
        null            | null      | DspType.SYSTEM_ASSIGN | null                                | new DspOrderVO(cityId: 1, createTime: new Date(), carTypeId: 1, orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])          || CheckCode.PASS
        null            | null      | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(60, -1)   | new DspOrderVO(cityId: 1, createTime: DateUtil.addHours(new Date(), -1), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)]) || CheckCode.PASS
        null            | null      | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(3600, -1) | new DspOrderVO(cityId: 1, carTypeId: null, createTime: new Date(), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])       || CheckCode.DRV_CAR_TYPE_NOT_MATCH
        null            | null      | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(3600, -1) | new DspOrderVO(cityId: 1, carTypeId: 0, createTime: new Date(), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])          || CheckCode.DRV_CAR_TYPE_NOT_MATCH
        null            | null      | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(3600, -1) | new DspOrderVO(cityId: 1, carTypeId: 117, createTime: new Date(), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])        || CheckCode.DRV_CAR_TYPE_NOT_MATCH
        0               | 0         | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(3600, -1) | new DspOrderVO(cityId: 1, carTypeId: 117, createTime: new Date(), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])        || CheckCode.DRV_CAR_TYPE_NOT_MATCH
        118             | 0         | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(3600, -1) | new DspOrderVO(cityId: 1, carTypeId: 117, createTime: new Date(), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])        || CheckCode.DRV_CAR_TYPE_NOT_MATCH
        118             | 118       | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(3600, -1) | new DspOrderVO(cityId: 1, carTypeId: 117, createTime: new Date(), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])        || CheckCode.DRV_CAR_TYPE_NOT_MATCH
        117             | 117       | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(3600, -1) | new DspOrderVO(cityId: 1, carTypeId: 117, createTime: new Date(), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])        || CheckCode.PASS
        117             | 117       | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(-1, -1)   | new DspOrderVO(cityId: 1, carTypeId: 117, createTime: new Date(), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])        || CheckCode.PASS
        117             | 117       | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(-1, 1440)  | new DspOrderVO(cityId: 1, carTypeId: 117, createTime: new Date(),estimatedUseTimeBj: DateUtil.addHours(new Date(), 25), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])        || CheckCode.PASS
        118             | 117       | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(-1, 1440)  | new DspOrderVO(cityId: 1, carTypeId: 117, createTime: new Date(),estimatedUseTimeBj: DateUtil.addHours(new Date(), 25), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])        || CheckCode.DRV_CAR_TYPE_NOT_MATCH
        118             | 117       | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(-1, 1440)  | new DspOrderVO(cityId: 1, carTypeId: 117, createTime: new Date(),estimatedUseTimeBj: DateUtil.addHours(new Date(), 20), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])        || CheckCode.PASS
        118             | 117       | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(180, 1440)  | new DspOrderVO(cityId: 1, carTypeId: 117, createTime: DateUtil.addSeconds(new Date(), -181),estimatedUseTimeBj: DateUtil.addHours(new Date(), 25), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])        || CheckCode.DRV_CAR_TYPE_NOT_MATCH
        118             | 117       | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(180, 1440)  | new DspOrderVO(cityId: 1, carTypeId: 117, createTime: DateUtil.addHours(new Date(), -181),estimatedUseTimeBj: DateUtil.addHours(new Date(), 20), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])        || CheckCode.PASS
        118             | 117       | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(180, 1440)  | new DspOrderVO(cityId: 1, carTypeId: 117, createTime: DateUtil.addHours(new Date(), -175),estimatedUseTimeBj: DateUtil.addHours(new Date(), 20), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])        || CheckCode.PASS
        118             | 117       | DspType.SYSTEM_ASSIGN | new SameCarTypeDspValueVO(180, 1440)  | new DspOrderVO(cityId: 1, carTypeId: 117, createTime: DateUtil.addHours(new Date(), -175),estimatedUseTimeBj: DateUtil.addHours(new Date(), 25), orderExtendAttributeInfo: [new OrderExtendAttributeVO(attributeCode: OrderExtendAttributeCodeEnum.is_downgrade_car_type_order.name(), attributeValue: 0)])        || CheckCode.DRV_CAR_TYPE_NOT_MATCH
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
