package com.ctrip.dcs.domain.common.util

import com.ctrip.framework.ucs.client.ShardingKey
import com.dianping.cat.Cat
import org.junit.Assert
import org.unidal.cat.traceContext.TraceContext
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2025/4/29 16:46
 */
class CatUtilTest extends Specification {

    @Unroll
    def "test doWithUdlOverride"() {
        given:
        String udl = "zh_cn";
        String requestFrom = "TRIP";
        TraceContext traceContext = Cat.getOrCreateTraceContext()
        traceContext.add(ShardingKey.UDL.getTraceContextKey(),"en_us");
        traceContext.add(ShardingKey.RequestFrom.getTraceContextKey(),"CTRIP");

        when:
        CatUtil.doWithUdlOverride(() -> "hhh", udl, requestFrom)
        then:

        Assert.assertTrue(Objects.equals(traceContext.get(ShardingKey.UDL.getTraceContextKey()), "en_us"))
        Assert.assertTrue(Objects.equals(traceContext.get(ShardingKey.RequestFrom.getTraceContextKey()), "CTRIP"))
    }

    @Unroll
    def "test writeDrvUdlAndUid"() {
        given:
        when:
        CatUtil.writeDrvUdlAndRequestFrom(drvUdl,"")
        then:
        Assert.assertTrue(Objects.equals(Cat.getOrCreateTraceContext().get(ShardingKey.UDL.getTraceContextKey()), expect));
        where:
        drvUdl  || expect
        "en_us" || "en_us"
    }
}
