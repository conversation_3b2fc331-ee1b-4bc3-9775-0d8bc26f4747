package com.ctrip.dcs.domain.common.enums


import spock.lang.Specification

class ContentTypeEnumTest extends Specification {


    def "test 1"() {
        when:
        def code = ContentTypeEnum.common.getCode()
        def des = ContentTypeEnum.common.getDesc()

        then:
        code > 0 == false
        des.length() > 0 == true
    }

    def "test 2"() {
        when:
        def code = ContentTypeEnum.emile.getCode()
        def des = ContentTypeEnum.emile.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 3"() {
        when:
        def code = ContentTypeEnum.sms.getCode()
        def des = ContentTypeEnum.sms.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 4"() {
        when:
        def code = ContentTypeEnum.event.getCode()
        def des = ContentTypeEnum.event.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 5"() {
        when:
        def code = ContentTypeEnum.complaint.getCode()
        def des = ContentTypeEnum.complaint.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 6"() {
        when:
        def code = ContentTypeEnum.conversationSummary.getCode()
        def des = ContentTypeEnum.conversationSummary.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 7"() {
        when:
        def code = ContentTypeEnum.callIn.getCode()
        def des = ContentTypeEnum.callIn.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 8"() {
        when:
        def code = ContentTypeEnum.exhale.getCode()
        def des = ContentTypeEnum.exhale.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }

    def "test 9"() {
        when:
        def code = ContentTypeEnum.newPenaltyList.getCode()
        def des = ContentTypeEnum.newPenaltyList.getDesc()

        then:
        code > 0 == true
        des.length() > 0 == true
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme