package com.ctrip.dcs.domain.common.util

import cn.hutool.core.date.LocalDateTimeUtil
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DriverWorkTimeVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class DriverWorkTimeUtilTest extends Specification {

    DriverVO driver = Mock(DriverVO)




    @Unroll
    def "test GetDriverWorkTimeTest"() {
        given:
        driver.getDriverId() >> 1L
        driver.getWorkTimes() >> new PeriodsVO(periods)

        when:
        DriverWorkTimeVO workTime = DriverWorkTimeUtil.getDriverWorkTime(driver, DateUtil.parseDateStr2Date(date))
        then: "验证返回结果里属性值是否符合预期"
        DateUtil.formatDate(workTime.getStart(), DateUtil.DATETIME_FORMAT) == start
        DateUtil.formatDate(workTime.getEnd(), DateUtil.DATETIME_FORMAT) == end

        where: "表格方式验证多种分支调用场景"
        date                  | periods                                       || start                 | end
        "2024-12-11 12:00:00" | ["08:00~20:00"]                               || "2024-12-11 08:00:00" | "2024-12-11 20:00:00"
        "2024-12-11 12:00:00" | ["10:00~23:59", "00:00~01:00"]                || "2024-12-11 10:00:00" | "2024-12-12 01:00:00"
        "2024-12-11 12:00:00" | ["11:00~11:59", "12:00~01:00"]                || "2024-12-11 11:00:00" | "2024-12-12 01:00:00"
        "2024-12-11 12:00:00" | ["08:00~10:00", "12:00~16:00"]                || "2024-12-11 08:00:00" | "2024-12-11 16:00:00"
        "2024-12-11 12:00:00" | ["20:00~23:00", "03:00~13:00"]                || "2024-12-10 20:00:00" | "2024-12-11 13:00:00"
        "2024-12-11 16:00:00" | ["15:00~23:59", "00:00~01:59", "03:00~05:59"] || "2024-12-11 15:00:00" | "2024-12-12 05:59:00"
        "2024-12-11 00:30:00" | ["15:00~23:59", "00:00~01:59", "03:00~05:59"] || "2024-12-10 15:00:00" | "2024-12-11 05:59:00"
        "2024-12-11 04:30:00" | ["15:00~23:59", "00:00~01:59", "03:00~05:59"] || "2024-12-10 15:00:00" | "2024-12-11 05:59:00"
    }

    @Unroll
    def "test getDriverWorkStartTime&getDriverWorkEndTime"() {
        given:
        driver.getWorkTimes() >> new PeriodsVO(periods)
        when:
        var startWorkTime = DriverWorkTimeUtil.getDriverWorkStartTime(driver, DateUtil.parseDateStr2Date(date))
        var endWorkTime = DriverWorkTimeUtil.getDriverWorkEndTime(driver, DateUtil.parseDateStr2Date(date))
        then: "验证返回结果里属性值是否符合预期"
        Optional.ofNullable(startWorkTime).map { e -> LocalDateTimeUtil.format(e, DateUtil.DATETIME_FORMAT) }.orElse("null") == start
        Optional.ofNullable(endWorkTime).map { e -> LocalDateTimeUtil.format(e, DateUtil.DATETIME_FORMAT) }.orElse("null") == end
        where: "表格方式验证多种分支调用场景"
        date | periods | start | end
        "2024-12-11 06:00:00" | ["08:00~20:00"]                               | "null" | "null"
        "2024-12-11 12:00:00" | ["08:00~20:00"]                               | "2024-12-11 08:00:00" | "2024-12-11 20:00:00"
        "2024-12-11 12:00:00" | ["10:00~23:59", "00:00~01:00"]                | "2024-12-11 10:00:00" | "2024-12-12 01:00:00"
        "2024-12-11 12:00:00" | ["11:00~11:59", "12:00~01:00"]                | "2024-12-11 11:00:00" | "2024-12-12 01:00:00"
        "2024-12-11 12:00:00" | ["08:00~10:00", "12:00~16:00"]                | "2024-12-11 08:00:00" | "2024-12-11 16:00:00"
        "2024-12-11 12:00:00" | ["20:00~23:00", "03:00~13:00"]                | "2024-12-10 20:00:00" | "2024-12-11 13:00:00"
        "2024-12-11 16:00:00" | ["15:00~23:59", "00:00~01:59", "03:00~05:59"] | "2024-12-11 15:00:00" | "2024-12-12 05:59:00"
        "2024-12-11 00:30:00" | ["15:00~23:59", "00:00~01:59", "03:00~05:59"] | "2024-12-10 15:00:00" | "2024-12-11 05:59:00"
        "2024-12-11 04:30:00" | ["15:00~23:59", "00:00~01:59", "03:00~05:59"] | "2024-12-10 15:00:00" | "2024-12-11 05:59:00"
    }
}
