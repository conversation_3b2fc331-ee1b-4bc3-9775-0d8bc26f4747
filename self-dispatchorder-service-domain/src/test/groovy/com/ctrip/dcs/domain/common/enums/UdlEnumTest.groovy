package com.ctrip.dcs.domain.common.enums

import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2025/4/29 15:05
 */
class UdlEnumTest extends Specification {

    @Unroll
    def "test isSgp"() {
        given:
        when:
        boolean result = UdlEnum.isSgp(udlEnum.getValue())
        then:
        Assert.assertTrue(Objects.equals(result, expectedResult))
        where:
        udlEnum     || expectedResult
        UdlEnum.SGP || true
        UdlEnum.SHA || false
    }

}
