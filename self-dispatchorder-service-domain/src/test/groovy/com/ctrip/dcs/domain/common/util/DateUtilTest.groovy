package com.ctrip.dcs.domain.common.util

import org.junit.Assert
import spock.lang.*

/**
 * <AUTHOR>
 */
class DateUtilTest extends Specification {

    @Unroll
    def "test convertStringToDateTime"() {
        given:
        String queryDate = "2025-05-01 10:00:00"
        String yyyyMmDd = "yyyy-MM-dd HH:mm:ss";
        when:
        def result = DateUtil.convertStringToDateTime(queryDate, yyyyMmDd)
        then:
        Assert.assertTrue(Objects.nonNull(result))
    }

    @Unroll
    def "test convertStringToDate"() {
        given:
        String queryDate = "2025-05-01";
        String yyyyMmDd = "yyyy-MM-dd";
        when:
        def result = DateUtil.convertStringToDate(queryDate, yyyyMmDd)
        then:
        Assert.assertTrue(Objects.nonNull(result))
    }

    @Unroll
    def "test convertDateToString"() {
        given:
        when:
        def localDate = DateUtil.convertStringToDateTime("2025-05-01 10:00:00", DateUtil.DATETIME_FORMAT);
        def result = DateUtil.convertDateToString(localDate, DateUtil.DATETIME_FORMAT)
        then:
        Assert.assertTrue(Objects.equals(result, "2025-05-01 10:00:00"))
    }
}