package com.ctrip.dcs.domain.common.enums

import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderPushRewardRuleDO
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class GrabDspOrderPushRewardTypeTest extends Specification {

    @Unroll
    def "test calculate for #rewardType"() {
        given:
        GrabDspOrderPushRewardRuleDO rule = new GrabDspOrderPushRewardRuleDO()
        rule.setRate(rate)
        rule.setAmount(amount)

        when:
        BigDecimal result = rewardType.calculate(rule, baseAmount, times)

        then:
        result == expected

        where:
        rewardType                       | rate                 | amount                | baseAmount            | times || expected
        GrabDspOrderPushRewardType.VALUE | null                 | new BigDecimal("50")  | new BigDecimal("200") | 3     || new BigDecimal("150")
        GrabDspOrderPushRewardType.VALUE | null                 | new BigDecimal("100") | new BigDecimal("250") | 2     || new BigDecimal("200")
        GrabDspOrderPushRewardType.VALUE | null                 | new BigDecimal("100") | new BigDecimal("250") | 3     || new BigDecimal("250")
        GrabDspOrderPushRewardType.RATE  | new BigDecimal("10") | null                  | new BigDecimal("300") | 2     || new BigDecimal("60")
        GrabDspOrderPushRewardType.RATE  | new BigDecimal("20") | null                  | new BigDecimal("400") | 1     || new BigDecimal("80")
        GrabDspOrderPushRewardType.RATE  | new BigDecimal("20") | null                  | new BigDecimal("400") | 50    || new BigDecimal("400")
    }
}
