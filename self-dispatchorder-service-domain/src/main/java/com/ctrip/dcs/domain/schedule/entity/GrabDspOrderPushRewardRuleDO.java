package com.ctrip.dcs.domain.schedule.entity;

import com.ctrip.dcs.domain.common.enums.GrabDspOrderPushRewardType;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class GrabDspOrderPushRewardRuleDO {

    /**
     * 奖励轮次
     */
    private Integer round;

    /**
     * 奖励类型
     */
    private GrabDspOrderPushRewardType type;

    /**
     * 品类
     */
    private BigDecimal rate;

    /**
     * 供应商名称
     */
    private BigDecimal amount;

    public Integer getRound() {
        return round;
    }

    public void setRound(Integer round) {
        this.round = round;
    }

    public GrabDspOrderPushRewardType getType() {
        return type;
    }

    public void setType(GrabDspOrderPushRewardType type) {
        this.type = type;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal calculate(BigDecimal baseAmount, int times) {
        return type.calculate(this, baseAmount, times);
    }
}
