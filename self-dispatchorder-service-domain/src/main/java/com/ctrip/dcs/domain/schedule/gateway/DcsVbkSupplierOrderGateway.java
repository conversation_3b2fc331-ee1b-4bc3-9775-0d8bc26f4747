package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.common.value.DspOrderVO;

public interface DcsVbkSupplierOrderGateway {

    /**
     * 订单是否允许更改
     *
     * @param orderStatus
     * @param orderStatusDetail
     * @param categoryCode
     * @param supplierId
     * @param takenType
     * @param shortDisOrder
     */
    Boolean queryOrderCanChangeDriver(Integer orderStatus, Integer orderStatusDetail, String categoryCode, Integer supplierId, Integer takenType, Integer shortDisOrder);

    /**
     * 订单是否允许更改
     * @param dspOrderVO
     * @param supplierId
     * @param takenType
     * @param newProcess
     * @return
     */
    Boolean queryOrderCanChangeDriverNew(DspOrderVO dspOrderVO, Integer supplierId, Integer takenType, Boolean newProcess, Integer shortDisOrder);

}
