package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2023/3/23 10:49
 */
public interface HighPriorityDriverGateway {


    /**
     * 获取头部司机
     * @param driverIds 司机id集合
     * @param cityId  城市id
     * @return
     */
     List<Long> queryHighPriorityDriver(List<DriverVO> driverIds, Integer cityId,Map<Long,DriverPointsVO> driverPoints);
}
