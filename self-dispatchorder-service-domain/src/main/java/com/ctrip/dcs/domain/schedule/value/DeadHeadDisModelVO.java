package com.ctrip.dcs.domain.schedule.value;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DriverWorkTimeVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.EmptyDrivingVO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
public class DeadHeadDisModelVO {
    private Long drvId;

    //当前待派发订单
    private String curDspOrderId;

    //家庭住址经纬度
    private BigDecimal addressLongitude;
    private  BigDecimal addressLatitude;


    //司机运力组id集合
    private String drvTransportGroupIds;

    //运力组配置空驶值
    private String  deadHeadDisLimitConf;


    /**
     * 订单命中的工作开始
     */
    private Date frameTimeStart;

    /**
     * 订单命中工作结束
     */
    private Date frameTimeEnd;

    /**
     * 前向单信息
     */
    private DspOrderVO frowardOrderInfo;
    /**
     * 后向订单信息
     */
    private DspOrderVO backwardOrderInfo;
    /**
     * 是否首单
     */
    private boolean begin;

    /**
     * 是否尾单
     */
    private boolean end;
    /**
     * 是否首尾单  1是
     */
    private int ifBeginAndEnd;

    /**
     *
     * 前向位置经纬度
     */
    private BigDecimal preLongitude;
    private BigDecimal preLatitude;


    /**
     * 订单开始位置经纬度
     */
    private BigDecimal fromLongitude;
    private BigDecimal fromLatitude;


    /**
     * 订单结束位置经纬度
     */
    private BigDecimal toLongitude;
    private BigDecimal toLatitude;

    /**
     * 后向位置经纬度
     */
    private BigDecimal backLongitude;

    private BigDecimal backLatitude;


    /**
     * 前向空驶信息
     */
    private EmptyDrivingVO frowardEmptyDrivingInfo;

    /**
     * 后向空驶信息
     */
    private EmptyDrivingVO backwardEmptyDrivingInfo;

    /**
     * 空驶距离（km）
     */
    private double deadHeadDis;
    /**
     * 使用司机实时点位计算前向空驶，
     */
    private Boolean realTimePosition ;

    public DeadHeadDisModelVO(DspOrderVO dspOrder, DriverVO driverInfo,DriverWorkTimeVO hitWorkTime) {
        this.curDspOrderId =dspOrder.getDspOrderId();
        this.drvId =driverInfo.getDriverId();
        this.fromLatitude =dspOrder.getActualFromLatitude();
        this.fromLongitude =dspOrder.getActualFromLongitude();
        this.toLatitude =dspOrder.getActualToLatitude();
        this.toLongitude =dspOrder.getActualToLongitude();
        this.ifBeginAndEnd =1;
        this.begin =true;
        this.end = true;
        this.frameTimeStart =hitWorkTime.getStart();
        this.frameTimeEnd =hitWorkTime.getEnd();
        this.realTimePosition = false;
        this.addressLatitude = new BigDecimal(driverInfo.getAddressLatitude().toString());
        this.addressLongitude = new BigDecimal(driverInfo.getAddressLongitude().toString());
    }

    public DeadHeadDisModelVO(DspOrderVO dspOrder, DriverVO driverInfo) {
        this.curDspOrderId = dspOrder.getDspOrderId();
        this.drvId = driverInfo.getDriverId();
        this.fromLatitude = dspOrder.getActualFromLatitude();
        this.fromLongitude = dspOrder.getActualFromLongitude();
        this.toLatitude = dspOrder.getActualToLatitude();
        this.toLongitude = dspOrder.getActualToLongitude();
        this.ifBeginAndEnd = 1;
        this.begin = true;
        this.end = true;
        this.realTimePosition = false;
        this.addressLatitude = new BigDecimal(driverInfo.getAddressLatitude().toString());
        this.addressLongitude = new BigDecimal(driverInfo.getAddressLongitude().toString());
    }

    public static boolean validateLongLat(BigDecimal latitude,BigDecimal longitude){
        return latitude != null && longitude != null && latitude.compareTo(BigDecimal.ONE) > 0 && longitude.compareTo(BigDecimal.ONE) > 0;
    }

    /**
     * 前向空驶
     * 单位 km
     * @return
     */
    public double getFrowardEmptyDistance() {
        return getEmptyDistance(this.frowardEmptyDrivingInfo);
    }

    public double getFrowardEmptyDuration() {
        return getEmptyDuration(this.frowardEmptyDrivingInfo);
    }

    /**
     * 后向空驶
     * 单位 km
     * @return
     */
    public double getBackwardEmptyDistance() {
        return getEmptyDistance(this.backwardEmptyDrivingInfo);
    }

    public double getBackwardEmptyDuration() {
        return getEmptyDuration(this.backwardEmptyDrivingInfo);
    }


    private double getEmptyDistance(EmptyDrivingVO emptyDriving) {
        return Optional.ofNullable(emptyDriving).map(EmptyDrivingVO::getDistance).orElse(NumberUtils.INTEGER_ZERO) / 1000D;
    }

    private double getEmptyDuration(EmptyDrivingVO emptyDriving) {
        return Optional.ofNullable(emptyDriving).map(EmptyDrivingVO::getDuration).orElse(NumberUtils.INTEGER_ZERO) / 60D;
    }
}
