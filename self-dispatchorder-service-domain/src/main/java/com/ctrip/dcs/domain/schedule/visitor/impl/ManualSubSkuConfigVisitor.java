package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.ManualSubSkuConfigGateway;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

public class ManualSubSkuConfigVisitor extends AbstractVisitor {

    private DspOrderVO order;

    private ManualSubSkuConfigGateway manualSubSkuConfigGateway;

    public ManualSubSkuConfigVisitor(DspOrderVO order) {
        Assert.notNull(order);
        this.order = order;
        this.manualSubSkuConfigGateway = getInstance(ManualSubSkuConfigGateway.class);
    }

    public ManualSubSkuConfigVisitor(DspOrderVO order, ManualSubSkuConfigGateway gateway) {
        Assert.notNull(order);
        this.order = order;
        this.manualSubSkuConfigGateway = gateway;
    }

    @Override
    public void visit(CheckContext context) {
        Map<String, List<Integer>> map = context.getManualSubSkuConfigMap();
        if (map.containsKey(order.getDspOrderId())) {
            return;
        }
        List<Integer> list = manualSubSkuConfigGateway.queryManualSubSkuList();
        if (CollectionUtils.isNotEmpty(list)) {
            map.put(order.getDspOrderId(), list);
        }
    }
}
