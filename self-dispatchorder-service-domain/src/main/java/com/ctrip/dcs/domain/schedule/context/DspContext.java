package com.ctrip.dcs.domain.schedule.context;

import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.EmptyDrivingVO;
import com.ctrip.dcs.domain.schedule.check.CheckItemRecord;
import com.ctrip.dcs.domain.schedule.service.DspContextService;
import com.ctrip.dcs.domain.schedule.sort.SortRecord;
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 派单上下文
 * <AUTHOR>
 */
public class DspContext {

    @Getter
    private final Map<Long, List<DspOrderVO>> orderListMap =  Maps.newHashMap();

    @Getter
    private Map<Long, DriverRelateOrderVO> relateOrderMap = Maps.newHashMap();

    @Getter
    private Map<Long, DriverRelateOrderVO> relateOrderNoEmptyDriveLbsMap = Maps.newHashMap();

    /**
     * 新的前后单判断逻辑：http://idev.ctripcorp.com?5061154
     */
    @Getter
    private final Map<Long/*司机ID*/, DspOrderVO/*前向单*/> driverForwardOrderMap = new HashMap<>();

    @Getter
    private final Map<Long/*司机ID*/, EmptyDrivingVO/*前向单*/> driverForwardEmptyMap = new HashMap<>();

    @Getter
    private final Map<Long/*司机ID*/, DspOrderVO/*后向单*/> driverBackwardOrderMap = new HashMap<>();

    @Getter
    private final Map<Long, Map<Integer, Long>> driverDistanceOrderCountMap = Maps.newHashMap();

    @Getter
    private final Map<Long, Integer> driverDistanceTypeMap = Maps.newHashMap();

    @Getter
    private final Map<Long/*司机ID*/, EmptyDrivingVO/*前向单*/> driverBackwardEmptyMap = new HashMap<>();

    @Getter
    private final Map<Long/*司机ID*/, EmptyDrivingVO/*前向单*/> driverOriginalEmptyMap = new HashMap<>();

    /**
     * 司机分信息
     * key:司机id
     * value:司机分信息
     */
    @Getter
    private final Map<Long, DriverPointsVO> driverPointsMap = new HashMap<>();

    @Getter
    private DspContextService service;

    public DspContext(DspContextService service) {
        this.service = service;
    }


    /**
     * 记录派发过程信息
     */
    public void record(CheckItemRecord record) {
        if (Objects.isNull(record)) {
            return;
        }
        // ck
        this.service.record(record);
    }

    public void record(SortRecord record) {
        if (Objects.isNull(record)) {
            return;
        }
        // ck
        this.service.record(record);
    }
}
