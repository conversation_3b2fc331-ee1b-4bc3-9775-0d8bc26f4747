package com.ctrip.dcs.domain.schedule.process;

import com.ctrip.dcs.domain.common.enums.DspType;
import org.springframework.core.annotation.AliasFor;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * 派发处理器注解
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
@Inherited
@Component
public @interface Processor {

    @AliasFor(annotation = Component.class)
    String value();

    /**
     * 类型
     * @return
     */
    DspType type();
}
