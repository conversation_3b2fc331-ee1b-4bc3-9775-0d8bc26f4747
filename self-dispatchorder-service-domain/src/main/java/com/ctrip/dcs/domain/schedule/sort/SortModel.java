package com.ctrip.dcs.domain.schedule.sort;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.schedule.sort.feature.Value;
import com.ctrip.dcs.domain.schedule.sort.score.Scorer;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 待打分对象
 * <AUTHOR>
 */
@Getter
public class SortModel {

    private DspModelVO model;

    /**
     * 总分
     */
    private double score;

    /**
     * 特征值
     */
    private List<Value> values;

    public SortModel(DspModelVO model) {
        Assert.notNull(model);
        this.model = model;
        this.values = Lists.newArrayList();
    }

    /**
     * 添加特征值
     * @param value
     */
    public void addValue(Value value) {
        Assert.notNull(value);
        this.values.add(value);
    }

    /**
     * 计算总分
     * @param scorer 求和器
     */
    public void score(Scorer scorer) {
        this.score = scorer.sum(this.values);
    }

    /**
     * 清空特征计算结果
     */
    public void deleteValuesAndScore(){
        values = new ArrayList<>();
        score = 0;
    }

    /**
     * 批量清空特征值计算结果
     * @param sortModels
     */
    public static void batchDeleteValuesAndScore(List<SortModel> sortModels){
        for (SortModel sortModel : sortModels) {
            sortModel.deleteValuesAndScore();
        }
    }

    /**
     * 封装日志信息
     * @return
     */
    public Map<String,Object> log(){
        Map<String,Object> result = new HashMap<>();
        result.put("dspOrderId",getModel().getOrder().getDspOrderId());
        result.put("driverId",getModel().getDriver().getDriverId());
        result.put("score",getScore());
        result.put("values",getValues());
        return result;
    }

    /**
     * 批量封装日志
     * @param sortModels
     * @return
     */
    public static List<Map<String,Object>> batchLog(List<SortModel> sortModels){
        List<Map<String,Object>> result = new ArrayList<>();
        for (SortModel sortModel : sortModels) {
            result.add(sortModel.log());
        }
        return result;
    }
}
