package com.ctrip.dcs.domain.schedule.factory;

import com.ctrip.dcs.domain.common.enums.ScheduleType;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.value.ScheduleStrategyVO;

import java.util.List;

;


/**
 * 调度工厂
 *
 * <AUTHOR>
 */
public interface ScheduleTaskFactory {

    List<ScheduleTaskDO> create(ScheduleStrategyVO strategy, DspOrderVO order, ScheduleType scheduleType);
}
