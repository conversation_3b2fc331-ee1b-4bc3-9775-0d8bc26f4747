package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.enums.DspStage;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.gateway.OrderDisLimitServiceGateway;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.OrderDisLimitVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class OrderDistanceLimitVisitor extends AbstractVisitor {
    private static final Logger logger = LoggerFactory.getLogger(OrderDistanceLimitVisitor.class);

    private SubSkuVO subSku;

    private List<CheckModel> models;

    private DspOrderVO dspOrder;

    private DspStage dspStage;

    private OrderDisLimitServiceGateway orderDisLimitServiceGateway;

    public OrderDistanceLimitVisitor(DspOrderVO dspOrder, List<CheckModel> models, SubSkuVO subSku, DspStage dspStage) {
        Assert.notNull(dspOrder);
        Assert.notEmpty(models);
        Assert.notNull(subSku);
        Assert.notNull(dspStage);
        this.subSku = subSku;
        this.models = models;
        this.dspOrder = dspOrder;
        this.dspStage = dspStage;
        this.orderDisLimitServiceGateway = getInstance(OrderDisLimitServiceGateway.class);
    }

    public OrderDistanceLimitVisitor(SubSkuVO subSku, List<CheckModel> models, DspOrderVO dspOrder, DspStage dspStage, OrderDisLimitServiceGateway orderDisLimitServiceGateway) {
        this.subSku = subSku;
        this.models = models;
        this.dspOrder = dspOrder;
        this.dspStage = dspStage;
        this.orderDisLimitServiceGateway = orderDisLimitServiceGateway;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, OrderDisLimitVO> limitMap = context.getOrderDisLimitMap();
        List<CheckModel> filter = filter(this.models, limitMap);
        if (CollectionUtils.isNotEmpty(filter)) {
            Map<Long, OrderDisLimitVO> result = orderDisLimitServiceGateway.queryOrderDisLimitMap(filter, dspOrder, dspStage, subSku.getDspType(), context.getContext(), context.getDuid());
            limitMap.putAll(result);
        }
        logger.info("OrderDistanceLimitVisitor", JsonUtils.toJson(limitMap));
    }

    private List<CheckModel> filter(List<CheckModel> models, Map<Long, OrderDisLimitVO> limitMap) {
        List<DriverVO> drivers = models.stream().map(CheckModel::getModel).map(DspModelVO::getDriver).collect(Collectors.toList());
        List<DriverVO> filter = filter(drivers, limitMap.keySet());
        Set<Long> driverIds = filter.stream().map(DriverVO::getDriverId).collect(Collectors.toSet());
        return models.stream().filter(m -> driverIds.contains(m.getModel().getDriver().getDriverId())).collect(Collectors.toList());
    }
}
