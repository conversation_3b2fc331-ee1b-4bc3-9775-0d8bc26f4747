package com.ctrip.dcs.domain.schedule.value;

import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Builder
public class ScheduleRecordVO {

    private Long scheduleId;

    private String dspOrderId;

    private Integer scheduleStatus;

    private Integer round;

    private String event;

    private List<ScheduleTaskRecordVO> taskRecords;

    public ScheduleRecordVO() {
    }

    public ScheduleRecordVO(Long scheduleId, String dspOrderId, Integer scheduleStatus, Integer round, String event, List<ScheduleTaskRecordVO> taskRecords) {
        this.scheduleId = scheduleId;
        this.dspOrderId = dspOrderId;
        this.scheduleStatus = scheduleStatus;
        this.round = round;
        this.event = event;
        this.taskRecords = taskRecords;
    }
}
