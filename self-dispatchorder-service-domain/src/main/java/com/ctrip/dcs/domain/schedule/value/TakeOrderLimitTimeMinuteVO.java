package com.ctrip.dcs.domain.schedule.value;

import org.apache.commons.lang3.ObjectUtils;

import java.util.concurrent.TimeUnit;

/**
 * 进单时间 单位:分钟
 */
public class TakeOrderLimitTimeMinuteVO {

    private final int minute;

    public TakeOrderLimitTimeMinuteVO(Integer minute) {
        this.minute = ObjectUtils.defaultIfNull(minute, 0);
    }

    public boolean greaterThanMilliseconds(Long duration) {
        return minute > TimeUnit.MILLISECONDS.toMinutes(ObjectUtils.defaultIfNull(duration, 0L));
    }
}
