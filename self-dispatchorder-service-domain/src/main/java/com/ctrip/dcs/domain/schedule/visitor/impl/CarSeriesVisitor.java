package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.dsporder.value.SeriesVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.CarSeriesGateway;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class CarSeriesVisitor extends AbstractVisitor {

    private DspOrderVO order;

    private CarSeriesGateway carSeriesGateway;

    public CarSeriesVisitor(DspOrderVO order) {
        Assert.notNull(order);
        this.order = order;
        this.carSeriesGateway = getInstance(CarSeriesGateway.class);
    }

    public CarSeriesVisitor(DspOrderVO order, CarSeriesGateway carSeriesGateway) {
        this.order = order;
        this.carSeriesGateway = carSeriesGateway;
    }

    @Override
    public void visit(CheckContext context) {
        Map<String, SeriesVO> carSeriesMap = context.getCarSeriesMap();
        if (carSeriesMap.containsKey(order.getDspOrderId())) {
            return;
        }
        if (CategoryCodeEnum.isCharterOrder(order.getCategoryCode().getType()) && order.isDesignatedVehicleOrder()) {
            // 包车指定车型订单查询车系
            SeriesVO series = carSeriesGateway.query(order.getCityId(), order.getCarTypeId());
            carSeriesMap.put(order.getDspOrderId(), series);
        }
    }
}
