package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.DriverOrderStatusEnum;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.DataSwitchVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.graborder.GrabOrderDTO;
import com.ctrip.dcs.domain.dsporder.value.TakenType;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface SelfOrderQueryGateway {

    DspOrderVO queryDspOrder(String dspOrderId);

    DspOrderVO queryDspOrder(String dspOrderId, DataSwitchVO dataSwitch);

    DspOrderVO queryDspOrderForVBKGrab(String dspOrderId, boolean confirmRecordSwitch);


    DspOrderVO queryOrderDetailForSchedule(String dspOrderId, boolean dspOrderFeeSwitch, boolean dspOrderDetailSwitch);

    BaseDetailVO queryOrderBaseDetail(String userOrderId, String dspOrderId);

    BaseDetailVO queryOrderBaseDetailForFlightProtect(String userOrderId);


    BaseDetailVO queryOrderBaseDetail(String dspOrderId);

    boolean currentStopFlag(String userOrderId, String day);

    /**
     * 查询允许调度抢单的供应商
     * @param isUrgentOrder 是否时急单
     * @return
     */
    List<Long> queryDispatcherGrabOrderSupplierIds(Boolean isUrgentOrder);


    /**
     * 根绝司机、派发单、duid获取抢单数据
     * @param orderId
     * @param duid
     * @param driverIds
     * @return
     */
    List<GrabOrderDTO> queryGrabDriverList(String orderId, String duid, Set<Long> driverIds);


    /**
     * 查询司机单用车时间最近的变更事件ID
     * @param driverOrderId 司机单ID
     */
    Long queryDriverOrderUseTimeRecentChangeEventId(String driverOrderId);

    /**
     * 查询司机某个时间段的长短公里订单数量
     *
     * @param driverIds            司机ID列表
     * @param categoryList         产线CODE列表
     * @param driverOrderStateList 司机单状态列表
     * @param takenTypeList        接单类型列表
     * @param useTimeRangeLeft     用车时间左区间（北京时间）
     * @param useTimeRangeRight    用车时间右区间（北京时间）
     * @return Map<司机ID, Map < 里程类型, 订单数量>>
     */
    Map<Long, Map<Integer, Long>> queryDriverDistanceOrderCount(List<Long> driverIds, List<CategoryCodeEnum> categoryList, List<DriverOrderStatusEnum> driverOrderStateList, List<TakenType> takenTypeList, LocalDateTime useTimeRangeLeft, LocalDateTime useTimeRangeRight);

}
