package com.ctrip.dcs.domain.schedule.value;

import com.ctrip.dcs.domain.common.enums.DiversionMatchEnum;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.google.common.collect.ImmutableMap;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public class SupplierOrderDiversionRecordVO {

    /**
     * 用户订单号
     */
    private String userOrderId;

    /**
     * 派发子单号
     */
    private String dspOrderId;

    /**
     * 供应商
     */
    public Long supplierId;

    /**
     * 配置分流
     */
    public BigDecimal configDiversionValue;

    /**
     * 实际分流
     */
    public BigDecimal actualDiversionValue;

    /**
     * 订单数量
     */
    private Integer supplierOrderCount;

    private Integer isConfigSupplier;

    private DiversionMatchEnum type;

    public SupplierOrderDiversionRecordVO(DspOrderVO dspOrder, SupplierDiversionVO supplierDiversion, BigDecimal actualDiversionValue) {
        this.userOrderId = dspOrder.getUserOrderId();
        this.dspOrderId = dspOrder.getDspOrderId();
        this.supplierId = supplierDiversion.getSupplierId();
        this.configDiversionValue = supplierDiversion.getDiversionValue();
        this.supplierOrderCount = supplierDiversion.getSupplierOrderCount();
        this.type = supplierDiversion.getType();
        this.isConfigSupplier = supplierDiversion.getIsConfigSupplier();
        this.actualDiversionValue = actualDiversionValue;
    }
}
