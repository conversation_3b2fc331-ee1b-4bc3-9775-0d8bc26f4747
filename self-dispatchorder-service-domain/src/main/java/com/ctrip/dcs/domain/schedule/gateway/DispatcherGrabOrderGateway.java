package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DispatcherGrabOrderGateway {

    List<DispatcherGrabOrderDO> create(List<DispatcherGrabOrderDO> orders);

    List<DispatcherGrabOrderDO> query(List<Long> ids);

    DispatcherGrabOrderDO query(String dspOrderId, Long supplierId);

    DispatcherGrabOrderDO query(Long id, Long supplierId);

    DispatcherGrabOrderDO query(String userOrderId, Integer modifyVersion);
}
