package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DriverRelateOrderVisitor extends AbstractVisitor {

    private DspOrderVO dspOrder;

    private List<DriverVO> drivers;

    private Integer timeInterval;

    private Boolean isEmptyDriveLbs;

    private QueryDspOrderService queryDspOrderService;

    public DriverRelateOrderVisitor(DspOrderVO dspOrder, List<DriverVO> drivers, Integer timeInterval,Boolean isEmptyDriveLbs) {
        Assert.notNull(dspOrder);
        Assert.notEmpty(drivers);
        Assert.notNull(timeInterval);
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.timeInterval = timeInterval;
        this.isEmptyDriveLbs = isEmptyDriveLbs;
        this.queryDspOrderService = getInstance(QueryDspOrderService.class);
    }

    public DriverRelateOrderVisitor(DspOrderVO dspOrder, List<DriverVO> drivers, Integer timeInterval, QueryDspOrderService queryDspOrderService) {
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.timeInterval = timeInterval;
        this.queryDspOrderService = queryDspOrderService;
    }

    @Override
    public void visit(CheckContext context) {
        if (Boolean.TRUE.equals(isEmptyDriveLbs)) {
            Map<Long, DriverRelateOrderVO> map = context.getContext().getRelateOrderMap();
            visit(map);
        }else {
            Map<Long, DriverRelateOrderVO> map = context.getContext().getRelateOrderNoEmptyDriveLbsMap();
            visit(map);
        }
    }

    @Override
    public void visit(SortContext context) {
        if (Boolean.TRUE.equals(isEmptyDriveLbs)) {
            Map<Long, DriverRelateOrderVO> map = context.getDspContext().getRelateOrderMap();
            visit(map);
        }else {
            Map<Long, DriverRelateOrderVO> map = context.getDspContext().getRelateOrderNoEmptyDriveLbsMap();
            visit(map);
        }
    }

    public void visit(Map<Long, DriverRelateOrderVO> map) {
        List<DriverVO> list = filter(drivers, map.keySet());
        List<Long> driverIds = list.stream().map(DriverVO::getDriverId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(driverIds)) {
            Map<Long, DriverRelateOrderVO> result = queryDspOrderService.queryRelateOrders(driverIds, dspOrder, timeInterval,isEmptyDriveLbs);
            map.putAll(result);
        }
    }
}
