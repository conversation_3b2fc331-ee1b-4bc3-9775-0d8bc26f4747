package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.DriverPointsGateway;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DriverPointsVisitor extends AbstractVisitor {

    private DspOrderVO order;

    private List<DriverVO> drivers;

    private DriverPointsGateway dcsDriverLevelServiceGateway;

    public DriverPointsVisitor(List<DriverVO> drivers) {
        Assert.notEmpty(drivers);
        this.drivers = drivers;
        this.dcsDriverLevelServiceGateway = getInstance(DriverPointsGateway.class);
    }

    public DriverPointsVisitor(DspOrderVO order, List<DriverVO> drivers) {
        Assert.notEmpty(drivers);
        this.order = order;
        this.drivers = drivers;
        this.dcsDriverLevelServiceGateway = getInstance(DriverPointsGateway.class);
    }

    public DriverPointsVisitor(List<DriverVO> drivers, DriverPointsGateway dcsDriverLevelServiceGateway) {
        this.drivers = drivers;
        this.dcsDriverLevelServiceGateway = dcsDriverLevelServiceGateway;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, DriverPointsVO> pointsMap = context.getContext().getDriverPointsMap();
        visit(pointsMap);
    }

    @Override
    public void visit(SortContext context) {
        Map<Long, DriverPointsVO> pointsMap = context.getDspContext().getDriverPointsMap();
        visit(pointsMap);
    }

    private void visit(Map<Long, DriverPointsVO> pointsMap) {
        List<DriverVO> filter = filter(drivers, pointsMap.keySet());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        List<Long> driverIds = filter.stream().map(DriverVO::getDriverId).collect(Collectors.toList());
        List<DriverPointsVO> points = Lists.newArrayList();
        String categoryCode = Optional.ofNullable(order).map(DspOrderVO::getCategoryCode).map(CategoryCodeEnum::getParentType).orElse("jnt");
        if (Objects.equals(ParentCategoryEnum.DAY.getCode(), categoryCode)) {
            // 包车订单，查询司导分
            points = dcsDriverLevelServiceGateway.batchQueryGuidePoints(driverIds);
        } else {
            // 非包车订单，查询司机分
            points = dcsDriverLevelServiceGateway.batchQueryDriverTotalPoints(driverIds);
        }
        if (CollectionUtils.isEmpty(points)) {
            return;
        }
        for (DriverPointsVO point : points) {
            pointsMap.put(point.getDriverId(), point);
        }
    }
}
