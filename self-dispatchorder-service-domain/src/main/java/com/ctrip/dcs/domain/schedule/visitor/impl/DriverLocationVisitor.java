package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.service.QueryDriverLocationService;
import com.ctrip.dcs.domain.common.value.DriverLocationVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DriverLocationVisitor extends AbstractVisitor {

    private List<DriverVO> drivers;

    private QueryDriverLocationService queryDriverLocationService;

    public DriverLocationVisitor(List<DriverVO> drivers) {
        Assert.notEmpty(drivers);
        this.drivers = drivers;
        this.queryDriverLocationService = getInstance(QueryDriverLocationService.class);
    }

    public DriverLocationVisitor(List<DriverVO> drivers, QueryDriverLocationService queryDriverLocationService) {
        this.drivers = drivers;
        this.queryDriverLocationService = queryDriverLocationService;
    }

    @Override
    public void visit(CheckContext context) {
        visit(context.getDriverLastLocationMap());
    }

    private void visit(Map<Long, DriverLocationVO> map) {
        List<DriverVO> list = filter(drivers, map.keySet());
        Set<Long> driverIds = list.stream().map(DriverVO::getDriverId).collect(Collectors.toSet());
        List<DriverLocationVO> locations = queryDriverLocationService.queryDriverLocation(driverIds);
        if (CollectionUtils.isNotEmpty(locations)) {
            for (DriverLocationVO location : locations) {
                map.put(location.getDriverId(), location);
            }
        }
    }

}
