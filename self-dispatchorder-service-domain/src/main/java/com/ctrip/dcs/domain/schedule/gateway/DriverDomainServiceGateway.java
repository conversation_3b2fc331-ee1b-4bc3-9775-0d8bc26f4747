package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.dsporder.value.DriverLeaveRightsVO;
import com.ctrip.dcs.domain.schedule.value.DriverLeaveRightsVo;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.schedule.value.DriverPushConfigVO;
import com.ctrip.dcs.domain.schedule.value.DriverRightsVO;

import java.util.List;

public interface DriverDomainServiceGateway {
    public List<DriverPushConfigVO> query(List<Long> driverIdList);

    /**
     * 查询司机是否具有改派权益
     *
     * @param driverId 司机id
     * @return 是否具有改派权益
     */
    Boolean queryDriverDispatchRights(Long driverId);

    /**
     * 使用司机权益
     * @param driverId
     * @param detailVO
     * @return
     */
    Boolean useDriverDispatchRight(Long driverId, BaseDetailVO detailVO);

    /**
     * 查询司机权益信息记录
     *
     * @param driverId
     * @return 临近权益信息
     */
    DriverRightsVO queryDriverRightsInfo(Long driverId);

    /**
     * 查询司机请假权益
     *
     * @param driverId 司机id
     * @return 司机请假权益信息
     */
    DriverLeaveRightsVO queryDriverLeaveRight(Long driverId);

}