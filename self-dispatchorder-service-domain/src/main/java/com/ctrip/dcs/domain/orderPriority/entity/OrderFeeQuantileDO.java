package com.ctrip.dcs.domain.orderPriority.entity;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class OrderFeeQuantileDO {

    private Integer cityId;

    private Integer carTypeId;

    private BigDecimal high;

    private BigDecimal medium;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getCarTypeId() {
        return carTypeId;
    }

    public void setCarTypeId(Integer carTypeId) {
        this.carTypeId = carTypeId;
    }

    public BigDecimal getHigh() {
        return high;
    }

    public void setHigh(BigDecimal high) {
        this.high = high;
    }

    public BigDecimal getMedium() {
        return medium;
    }

    public void setMedium(BigDecimal medium) {
        this.medium = medium;
    }
}
