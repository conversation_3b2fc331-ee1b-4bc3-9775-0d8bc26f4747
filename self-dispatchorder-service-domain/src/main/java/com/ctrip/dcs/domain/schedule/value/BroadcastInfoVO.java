package com.ctrip.dcs.domain.schedule.value;

import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.SupplierVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.google.common.collect.ImmutableMap;
import lombok.Getter;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * <AUTHOR>
 */
public class BroadcastInfoVO {

    @JsonProperty("pOrderId")
    private String pOrderId;

    @JsonProperty("sOrderId")
    private String sOrderId = "";

    private String orderMatchType = "SINGLE";

    private String pduid;

    private String sduid = "";

    private Long transportGroupId;

    private String transportGroupName;

    private Integer transportGroupMode;

    private Long supplierId;

    private String extendInfo;

    public BroadcastInfoVO(String dspOrderId, String duid, TransportGroupVO transportGroup, SupplierVO supplier) {
        this.pOrderId = dspOrderId;
        this.pduid = duid;
        this.transportGroupId = transportGroup.getTransportGroupId();
        this.transportGroupName = transportGroup.getTransportGroupName();
        this.transportGroupMode = transportGroup.getTransportGroupMode().getCode();
        this.supplierId = supplier.getSupplierId();
        this.extendInfo = JsonUtil.toJson(ImmutableMap.of("supplierId", supplier.getSupplierId()));
    }

    public BroadcastInfoVO(String dspOrderId, String duid) {
        this.pOrderId = dspOrderId;
        this.pduid = duid;
    }

    public String getpOrderId() {
        return pOrderId;
    }

    public String getsOrderId() {
        return sOrderId;
    }

    public String getOrderMatchType() {
        return orderMatchType;
    }

    public String getPduid() {
        return pduid;
    }

    public String getSduid() {
        return sduid;
    }

    public Long getTransportGroupId() {
        return transportGroupId;
    }

    public String getTransportGroupName() {
        return transportGroupName;
    }

    public Integer getTransportGroupMode() {
        return transportGroupMode;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public String getExtendInfo() {
        return extendInfo;
    }
}
