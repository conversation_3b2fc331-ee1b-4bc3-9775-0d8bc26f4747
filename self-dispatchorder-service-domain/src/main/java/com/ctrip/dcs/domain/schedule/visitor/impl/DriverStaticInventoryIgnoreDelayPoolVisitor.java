package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.ConflictGateway;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DriverStaticInventoryIgnoreDelayPoolVisitor extends AbstractVisitor {

    private DspOrderVO dspOrder;

    private List<DriverVO> drivers;

    private ConflictGateway conflictGateway;

    public DriverStaticInventoryIgnoreDelayPoolVisitor(DspOrderVO dspOrder, List<DriverVO> drivers) {
        Assert.notNull(dspOrder);
        Assert.notEmpty(drivers);
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.conflictGateway = getInstance(ConflictGateway.class);
    }

    public DriverStaticInventoryIgnoreDelayPoolVisitor(DspOrderVO dspOrder, List<DriverVO> drivers, ConflictGateway conflictGateway) {
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.conflictGateway = conflictGateway;
    }

    @Override
    public void visit(CheckContext context) {
        List<Long> list = context.getDriverStaticInventoryIgnoreDelay();
        List<DriverVO> filter = filter(drivers, Sets.newHashSet(list));
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        List<Long> driverIds = filter.stream().map(DriverVO::getDriverId).collect(Collectors.toList());
        String startTime = DateFormatUtils.format(new Date(dspOrder.getEstimatedUseTime().getTime()), "yyyy-MM-dd HH:mm:ss");
        String endTime = DateFormatUtils.format(new Date(dspOrder.getPredicServiceStopTime().getTime()), "yyyy-MM-dd HH:mm:ss");
        String categoryCode = dspOrder.getCategoryCode().getType();
        List<Long> inventoryDriverIds = conflictGateway.checkDriverInventory(driverIds, dspOrder.getDspOrderId(), startTime, endTime, true,categoryCode,dspOrder.getDriverOrderId(), dspOrder.getOriginalDspOrderId());
        if (CollectionUtils.isNotEmpty(inventoryDriverIds)) {
            list.addAll(inventoryDriverIds);
        }
    }
}
