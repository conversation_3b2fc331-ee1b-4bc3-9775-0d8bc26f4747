package com.ctrip.dcs.domain.schedule.repository;

import com.ctrip.dcs.domain.common.value.graborder.GrabOrderDTO;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface GrabCentreRepository {

    void save(List<GrabOrderDO> orders);

    GrabOrderDO find(String dspOrderId, Long driverId);

    void delete(String dspOrderId, Long driverId);

    void deleteAll(String dspOrderId);

    Set<Long> getOrderDriverMapping(String dspOrderId);

    void saveOrderDriverMapping(List<GrabOrderDO> orders);

    void saveDriverOrderList(List<GrabOrderDO> orders);
}
