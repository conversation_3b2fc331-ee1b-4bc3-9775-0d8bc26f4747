package com.ctrip.dcs.domain.schedule.context;

import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderPushRuleDO;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Builder
@Getter
public class DspOrderRewardStrategyContext {

    private DspOrderVO order;

    private String offlineCityIds;

    private VBKDriverGrabOrderDO vbkDriverGrabOrder;

    private GrabDspOrderPushRuleDO rule;
}
