package com.ctrip.dcs.domain.schedule.repository;

import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotStatusEnum;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface GrabDspOrderSnapshotRepository {

    GrabDspOrderSnapshotDO query(String dspOrderId);

    List<GrabDspOrderSnapshotDO> query(List<String> dspOrderIds, Long supplierId, List<Integer> grabStatus);

    void saveOrUpdate(GrabDspOrderSnapshotDO snapshot);

    int updateGrabStatus(GrabDspOrderSnapshotDO snapshot, GrabDspOrderSnapshotStatusEnum newStatus, GrabDspOrderSnapshotStatusEnum oldStatus);

    void updateSettlePriceAndDuid(GrabDspOrderSnapshotDO snapshot);

    void updateDriverVisibleRemark(GrabDspOrderSnapshotDO snapshot);
}
