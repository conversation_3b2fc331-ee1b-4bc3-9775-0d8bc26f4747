package com.ctrip.dcs.domain.schedule.value;


import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/7 13:56
 */
public class PerformanceConfigVO {
    /**
     * 进入待出发状态预留时间单位小时
     */
    public Integer performanceStatusReserveHour;
    /**
     * 司机未出发风险预留时间 单位分钟
     */
    public Integer driverNoSetOutRiskReserveMinute;
    
    /**
     * 司机未到达风险预留时间 单位分钟
     */
    public Integer driverNoArriveRiskReserveMinute;
    
    /**
     * 开启邮件提醒 true 开启
     */
    public Boolean openEmailRemind;
    
    /**
     * 开启ivr true开启
     */
    public Boolean openIvr;
    
    /**
     * 移动端旅游商家消息通知开启 true开启
     */
    public Boolean mobileSupplierRemind;
    
    /**
     * pc端站内信开启 true 开启
     */
    public Boolean pcStationNotice;
    
    public boolean isOpenEmailRemind() {
        return Objects.equals(this.openEmailRemind, Boolean.TRUE);
    }
    
    public boolean isOpenIvr() {
        return Objects.equals(this.openIvr, Boolean.TRUE);
    }
    
    public boolean isOpenMobileSupplierRemind() {
        return Objects.equals(this.mobileSupplierRemind, Boolean.TRUE);
    }
    
    public boolean isOpenPcStationNotice() {
        return Objects.equals(this.pcStationNotice, Boolean.TRUE);
    }
    
    
    public Integer getPerformanceStatusReserveHour() {
        return performanceStatusReserveHour;
    }
    
    public void setPerformanceStatusReserveHour(Integer performanceStatusReserveHour) {
        this.performanceStatusReserveHour = performanceStatusReserveHour;
    }
    
    public Integer getDriverNoSetOutRiskReserveMinute() {
        return driverNoSetOutRiskReserveMinute;
    }
    
    public void setDriverNoSetOutRiskReserveMinute(Integer driverNoSetOutRiskReserveMinute) {
        this.driverNoSetOutRiskReserveMinute = driverNoSetOutRiskReserveMinute;
    }
    
    public Integer getDriverNoArriveRiskReserveMinute() {
        return driverNoArriveRiskReserveMinute;
    }
    
    public void setDriverNoArriveRiskReserveMinute(Integer driverNoArriveRiskReserveMinute) {
        this.driverNoArriveRiskReserveMinute = driverNoArriveRiskReserveMinute;
    }
    
    public Boolean getOpenEmailRemind() {
        return openEmailRemind;
    }
    
    public void setOpenEmailRemind(Boolean openEmailRemind) {
        this.openEmailRemind = openEmailRemind;
    }
    
    public Boolean getOpenIvr() {
        return openIvr;
    }
    
    public void setOpenIvr(Boolean openIvr) {
        this.openIvr = openIvr;
    }
    
    public Boolean getMobileSupplierRemind() {
        return mobileSupplierRemind;
    }
    
    public void setMobileSupplierRemind(Boolean mobileSupplierRemind) {
        this.mobileSupplierRemind = mobileSupplierRemind;
    }
    
    public Boolean getPcStationNotice() {
        return pcStationNotice;
    }
    
    public void setPcStationNotice(Boolean pcStationNotice) {
        this.pcStationNotice = pcStationNotice;
    }
}
