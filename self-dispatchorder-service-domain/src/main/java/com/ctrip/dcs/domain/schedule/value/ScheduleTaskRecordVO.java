package com.ctrip.dcs.domain.schedule.value;

import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleTaskRecordVO {

    private Long taskId;

    private Integer taskStatus;

    private Integer taskRound;

    private Integer subSkuId;

    private String reward;

    private Integer priority;

    public Map<String, String> toMap(ScheduleRecordVO scheduleRecord) {
        return ImmutableMap.<String, String>builder()
                .put("schedule_id", scheduleRecord.getScheduleId().toString())
                .put("dsp_order_id", scheduleRecord.getDspOrderId())
                .put("schedule_status", scheduleRecord.getScheduleStatus().toString())
                .put("schedule_round", scheduleRecord.getRound().toString())
                .put("event", scheduleRecord.getEvent())
                .put("task_id", this.getTaskId().toString())
                .put("task_status", this.getTaskStatus().toString())
                .put("task_round", this.getTaskRound().toString())
                .put("sub_sku_id", this.getSubSkuId().toString())
                .put("reward", this.getReward())
                .put("property", this.getPriority().toString())
                .build();
    }

}
