package com.ctrip.dcs.domain.schedule.service;

import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;

import java.util.List;

/**
 * 运力推荐领域服务
 * <AUTHOR>
 */
public interface RecommendService {

    /**
     * 推荐
     * @param order
     * @param subSku
     * @return 排序好的订单与运力的匹配组合
     */
    List<SortModel> recommend(DspOrderVO order, SubSkuVO subSku, DuidVO duidVO);

}
