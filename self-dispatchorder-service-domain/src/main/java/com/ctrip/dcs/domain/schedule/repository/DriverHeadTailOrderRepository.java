package com.ctrip.dcs.domain.schedule.repository;

import com.ctrip.dcs.domain.common.value.DriverHeadOrderVO;
import com.ctrip.dcs.domain.common.value.DriverTailOrderVO;

import java.util.Date;

/**
 * <AUTHOR>
 */
public interface DriverHeadTailOrderRepository {

    void save(DriverHeadOrderVO driverHeadOrder);

    void save(DriverTailOrderVO driverTailOrder);

    DriverHeadOrderVO queryDriverHeadOrder(Long driverId, Date date);

    DriverTailOrderVO queryDriverTailOrder(Long driverId, Date date);

    void del(Long driverId, Date date);
}
