package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.common.value.DriverVO;

/**
 * 司导平台接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/24 14:53:25
 */
public interface DriverPlatformApiServiceGateway {

    /**
     * 根据ID查询供应商是否在司导平台灰度中
     *
     * @param supplierId 供应商ID
     * @return true-灰度中（走司导平台） false-非灰度（走用车供应链）
     */
    boolean queryGrayControl(Long supplierId);


    boolean checkDrvGuideGrayFlow(DriverVO driverVO);
}
