package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.enums.OrderExtendAttributeCodeEnum;
import com.ctrip.dcs.domain.common.enums.PackageTypeEnum;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.OrderExtendAttributeVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.dsporder.value.UseDays;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.SelfTransportInventoryGateway;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import com.google.common.base.Strings;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class TransGroupOrderNumLimitVisitor extends AbstractVisitor {

    private DspOrderVO dspOrder;

    private List<TransportGroupVO> transportGroups;

    private SelfTransportInventoryGateway selfTransportInventoryGateway;

    public TransGroupOrderNumLimitVisitor(DspOrderVO dspOrder, List<TransportGroupVO> transportGroups) {
        Assert.notNull(dspOrder);
        Assert.notEmpty(transportGroups);
        this.dspOrder = dspOrder;
        this.transportGroups = transportGroups;
        this.selfTransportInventoryGateway = getInstance(SelfTransportInventoryGateway.class);
    }

    public TransGroupOrderNumLimitVisitor(DspOrderVO dspOrder, List<TransportGroupVO> transportGroups, SelfTransportInventoryGateway selfTransportInventoryGateway) {
        this.dspOrder = dspOrder;
        this.transportGroups = transportGroups;
        this.selfTransportInventoryGateway = selfTransportInventoryGateway;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, Integer> inventoryMap = context.getTransportGroupInventoryMap();
        List<TransportGroupVO> filter = filter(inventoryMap.keySet(), transportGroups);
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        CategoryCodeEnum categoryCode = dspOrder.getCategoryCode();
        Date estimatedUseTime = dspOrder.getEstimatedUseTime();
        UseDays useDays = dspOrder.getUseDays();
        Integer cityId = dspOrder.getCityId();
        String locationCode = dspOrder.getTargetId();
        List<Long> transportGroupIds = filter.stream().map(TransportGroupVO::getTransportGroupId).collect(Collectors.toList());

        Boolean isCharteredRouteOrder = dspOrder.getPackageType() != null && PackageTypeEnum.LINE.getCode() == dspOrder.getPackageType().intValue();
        Boolean isNearOrder = null;

        if (CollectionUtils.isNotEmpty(dspOrder.getOrderExtendAttributeInfo())) {
            for (OrderExtendAttributeVO vo : dspOrder.getOrderExtendAttributeInfo()) {
                if (vo != null && OrderExtendAttributeCodeEnum.is_near_order.toString().equals(vo.getAttributeCode())) {
                    isNearOrder = Strings.isNullOrEmpty(vo.getAttributeValue()) ? null : Boolean.valueOf(vo.getAttributeValue());
                    isCharteredRouteOrder = Boolean.TRUE;
                }
            }
        }
        Map<Long, Integer> map = selfTransportInventoryGateway.queryTransportGroupUsableInventory(categoryCode, estimatedUseTime, useDays, cityId, locationCode, transportGroupIds, isCharteredRouteOrder, isNearOrder
                ,dspOrder);
        inventoryMap.putAll(map);
    }

    private List<TransportGroupVO> filter(Set<Long> filter, List<TransportGroupVO> transportGroups) {
        return transportGroups.stream()
                .filter(tg -> !filter.contains(tg.getTransportGroupId()))
                .collect(Collectors.toList());
    }
}
