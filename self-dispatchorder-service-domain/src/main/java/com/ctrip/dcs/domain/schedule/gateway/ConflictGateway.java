package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.dto.BufferConfig;
import com.ctrip.dcs.domain.schedule.value.DrvInventoryCheckResultVO;

import java.util.List;
import java.util.Map;

/**
 * 冲突
 * <AUTHOR>
 */
public interface ConflictGateway {

    /**
     * 司机订单冲突检查
     * @param order
     * @param driverIds
     * @param ignoreDelayOrder
     * @return
     */
    Map<Long, DrvInventoryCheckResultVO> checkDriverOrderConflict(DspOrderVO order, List<Long> driverIds, boolean ignoreDelayOrder);

    /**
     * 订单冲突检查
     * @param order
     * @param conflictOrder
     * @return 是否冲突
     */
    Boolean checkOrderConflict(DspOrderVO order, DspOrderVO conflictOrder, Boolean defaultValue);

    /**
     * 司机库存冲突检查
     *
     * @param driverIds
     * @param dspOrderId
     * @param startTime
     * @param endTime
     * @param originalDspOrderId
     * @return
     */
    List<Long>  checkDriverInventory(List<Long> driverIds, String dspOrderId, String startTime, String endTime, boolean ignoreDelayPool, String categoryCode, String driverOrderId, String originalDspOrderId);

    /**
     * 校验订单与司机已接订单的是否有时间冲突
     * @param order
     * @param driverIds
     * @return 有时间冲突的司机id
     */
    List<Long> checkDriverOrderTimeConflict(DspOrderVO order, List<Long> driverIds);

    Map<BufferConfig.Request, BufferConfig.Response> queryBufferConfig(List<BufferConfig.Request> conditions);

}
