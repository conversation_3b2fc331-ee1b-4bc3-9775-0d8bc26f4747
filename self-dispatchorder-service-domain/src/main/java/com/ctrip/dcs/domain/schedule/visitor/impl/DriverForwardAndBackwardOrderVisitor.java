package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.DriverWorkTimeUtil;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.helper.Cats;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.context.DspContext;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.spring.InstanceLocator;
import com.dianping.cat.Cat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class DriverForwardAndBackwardOrderVisitor extends AbstractVisitor {
    private static final Logger logger = LoggerFactory.getLogger(DriverForwardAndBackwardOrderVisitor.class);

    // 待派订单
    DspOrderVO dispatchOrder;
    // 司机列表
    List<DriverVO> driverList;
    // 订单服务
    QueryDspOrderService orderService;

    public DriverForwardAndBackwardOrderVisitor(DspOrderVO dispatchOrder, List<DriverVO> driverList) {
        this(dispatchOrder, driverList, InstanceLocator.getInstance(QueryDspOrderService.class));
    }

    public DriverForwardAndBackwardOrderVisitor(DspOrderVO dispatchOrder, List<DriverVO> driverList, QueryDspOrderService orderService) {
        this.dispatchOrder = dispatchOrder;
        this.driverList = driverList;
        this.orderService = orderService;
    }

    @Override
    public void visit(CheckContext context) {
        Cats.runOrCatch("DriverForwardAndBackwardOrderVisitor", "CheckChain", () -> visit(context.getContext()));
    }

    @Override
    public void visit(SortContext context) {
        Cats.runOrCatch ("DriverForwardAndBackwardOrderVisitor", "SortChain", () -> visit(context.getDspContext()));
    }

    /**
     * 司机前后向单判断逻辑
     */
    public void visit(DspContext dspContext) {
        // 司机ID列表
        List<Long> driverIds = driverList.stream().map(DriverVO::getDriverId).collect(Collectors.toList());
        // 查询所有司机在该待派订单预估用车时间前后12H内[已接&未完成服务]相邻单
        Map<Long, DriverRelateOrderVO> relateOrders = orderService.queryRelateOrders(driverIds, dispatchOrder, 12, getRelateOrderStatusList());
        logger.info("DriverForwardAndBackwardOrderVisitor", "relateOrders:{}", JsonUtil.toJson(relateOrders));
        // 依次判断每个司机的相邻单情况
        for (DriverVO driver : driverList) {
            // 获取司机的相邻单
            DriverRelateOrderVO relateOrder = relateOrders.get(driver.getDriverId());
            // 检查是否有前向单
            if (checkFrowardOrder(dispatchOrder, driver, relateOrder)) {
                // 写入上下文
                dspContext.getDriverForwardOrderMap().put(driver.getDriverId(), relateOrder.getFrowardOrderInfo());
                dspContext.getDriverForwardEmptyMap().put(driver.getDriverId(), relateOrder.getFrowardEmptyDrivingInfo());
            }
            // 检查是否有后向单
            if (checkBackwardOrder(dispatchOrder, driver, relateOrder)) {
                // 写入上下文
                dspContext.getDriverBackwardOrderMap().put(driver.getDriverId(), relateOrder.getBackwardOrderInfo());
                dspContext.getDriverBackwardEmptyMap().put(driver.getDriverId(), relateOrder.getBackwardEmptyDrivingInfo());
            }
            if (relateOrder != null) {
                dspContext.getDriverOriginalEmptyMap().put(driver.getDriverId(), relateOrder.getOriginalEmptyDrivingInfo());
            }
        }
    }

    /**
     * 前向单判断逻辑：[司机工作开始时间-2小时]～[待派订单预估用车时间]之间是否有[已接&未完成服务]的订单
     * @param dispatchOrder 待派订单
     * @param driver        司机
     * @param relateOrder   相邻单
     */
    protected boolean checkFrowardOrder(DspOrderVO dispatchOrder, DriverVO driver, DriverRelateOrderVO relateOrder) {
        // 获取前向单
        DspOrderVO frowardOrder = Optional.ofNullable(relateOrder).map(DriverRelateOrderVO::getFrowardOrderInfo).orElse(null);
        // 没有前向单
        if (Objects.isNull(frowardOrder)) {
            Cat.logEvent("DriverForwardAndBackwardOrderVisitor", "NoFrowardOrder");
            return false;
        }
        // 司机开始工作时间
        LocalDateTime driverStartWorkTime = DriverWorkTimeUtil.getDriverWorkStartTime(driver, dispatchOrder.getEstimatedUseTime());
        // 如果获取不到司机开始工作时间则认为是没有前向单
        if (Objects.isNull(driverStartWorkTime)) {
            Cat.logEvent("DriverForwardAndBackwardOrderVisitor", "NoStartWorkTime");
            return false;
        }
        // 待派订单预估使用时间
        LocalDateTime dispatchOrderEstimatedUseTime = LocalDateTimeUtil.of(dispatchOrder.getEstimatedUseTime());
        // 前向单预估使用时间
        LocalDateTime frowardOrderEstimatedUseTime = LocalDateTimeUtil.of(frowardOrder.getEstimatedUseTime());
        // 判断是否在区间内
        return LocalDateTimeUtil.isIn(frowardOrderEstimatedUseTime, driverStartWorkTime.minusHours(2L), dispatchOrderEstimatedUseTime, true, true);
    }

    /**
     * 后向单判断逻辑：[待派订单预估用车时间～司机工作结束时间+2小时]之间是否有[已接&未完成服务]的订单
     * @param dispatchOrder 待派订单
     * @param driver        司机
     * @param relateOrder   相邻单
     */
    protected boolean checkBackwardOrder(DspOrderVO dispatchOrder, DriverVO driver, DriverRelateOrderVO relateOrder) {
        // 获取后向单
        DspOrderVO backwardOrder = Optional.ofNullable(relateOrder).map(DriverRelateOrderVO::getBackwardOrderInfo).orElse(null);
        // 没有后向单
        if (Objects.isNull(backwardOrder)) {
            Cat.logEvent("DriverForwardAndBackwardOrderVisitor", "NoBackwardOrder");
            return false;
        }
        // 司机结束工作时间
        LocalDateTime driverWorkEndTime = DriverWorkTimeUtil.getDriverWorkEndTime(driver, dispatchOrder.getEstimatedUseTime());
        // 如果获取不到司机开始结束时间则认为是没有后向单
        if (Objects.isNull(driverWorkEndTime)) {
            Cat.logEvent("DriverForwardAndBackwardOrderVisitor", "NoWorkEndTime");
            return false;
        }
        // 待派订单预估使用时间
        LocalDateTime dispatchOrderEstimatedUseTime = LocalDateTimeUtil.of(dispatchOrder.getEstimatedUseTime());
        // 后向单预估使用时间
        LocalDateTime backwardOrderEstimatedUseTime = LocalDateTimeUtil.of(backwardOrder.getEstimatedUseTime());
        // 判断是否在区间内
        return LocalDateTimeUtil.isIn(backwardOrderEstimatedUseTime, dispatchOrderEstimatedUseTime, driverWorkEndTime.plusHours(2L), true, true);
    }

    /**
     * 【已接单&未完成服务】240~500
     */
    protected List<OrderStatusEnum> getRelateOrderStatusList() {
        return List.of(OrderStatusEnum.DRIVER_CAR_CONFIRMED, OrderStatusEnum.DRIVER_TO_MEET, OrderStatusEnum.DRIVER_ARRIVE, OrderStatusEnum.DRIVER_SERVICE_START, OrderStatusEnum.DRIVER_SERVICE_END);
    }

}
