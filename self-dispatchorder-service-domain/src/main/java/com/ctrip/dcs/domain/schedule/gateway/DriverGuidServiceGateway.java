package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.common.value.DriverGuidVehicleRequestVO;
import com.ctrip.dcs.domain.common.value.DriverInfoRequestVO;
import com.ctrip.dcs.domain.dsporder.value.DriverInfoTypeVO;
import com.ctrip.dcs.domain.dsporder.value.DrvGuidVehVehicleVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/7 16:34
 */
public interface DriverGuidServiceGateway {
    
    
    List<DriverInfoTypeVO> getDriverInfos(DriverInfoRequestVO request);
    
    DriverInfoTypeVO getDriverInfo(Long driverId, String drvUdl, Long supplierId);
    
    
    List<Long> queryDriverIdBySupplier(Long cityId, Long supplierId);
    
    
    DrvGuidVehVehicleVO getVehicleInfo(Long vehicleId);
    
    /**
     *  查询车辆
     * @param request
     * @return
     */
    List<DrvGuidVehVehicleVO> getVehicleInfoList(DriverGuidVehicleRequestVO request);
    
    
}
