package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.CarTypeRelationGateway;
import com.ctrip.dcs.domain.schedule.value.CarTypeLevelRelationsVO;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class CarTypeLevelRelationVisitor extends AbstractVisitor {

    private Integer carTypeId;

    private CarTypeRelationGateway carTypeRelationGateway;

    public CarTypeLevelRelationVisitor(Integer carTypeId) {
        Assert.notNull(carTypeId);
        this.carTypeId = carTypeId;
        this.carTypeRelationGateway = getInstance(CarTypeRelationGateway.class);
    }

    public CarTypeLevelRelationVisitor(Integer carTypeId, CarTypeRelationGateway carTypeRelationGateway) {
        this.carTypeId = carTypeId;
        this.carTypeRelationGateway = carTypeRelationGateway;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Integer, CarTypeLevelRelationsVO> map = context.getCarTypeLevelRelationsMap();
        if (map.containsKey(carTypeId)) {
            return;
        }
        CarTypeLevelRelationsVO relations = carTypeRelationGateway.queryCarTypeLevelRelations(carTypeId);
        map.put(carTypeId, relations);
    }
}
