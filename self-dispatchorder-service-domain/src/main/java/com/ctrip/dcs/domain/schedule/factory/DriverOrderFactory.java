package com.ctrip.dcs.domain.schedule.factory;

import com.ctrip.dcs.domain.common.value.DriverOrderVO;
import com.ctrip.dcs.domain.common.value.SupplierVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.common.value.VehicleVO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.schedule.entity.DspOrderRewardStrategyDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.RedispatchOrderVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;

import java.math.BigDecimal;

/**
 * 司机单工厂类
 * <AUTHOR>
 */
public interface DriverOrderFactory {

    // 创建司机单（原单修改场景）
    DriverOrderVO createForModify(DspModelVO dspModel, SubSkuVO oldSubSku, Long oldDriverId, String oldDriverOrderId, Integer modifyVersion);

    /**
     * 创建司机单
     * @param dspModel
     * @param task
     * @return
     */
    DriverOrderVO create(DspModelVO dspModel, ScheduleTaskDO task);

    /**
     * 指定供应商创建司机单
     * @param dspModel
     * @param supplier
     * @param transportGroup
     * @param task
     * @return
     */
    DriverOrderVO createBySupplier(DspModelVO dspModel, SupplierVO supplier, TransportGroupVO transportGroup, ScheduleTaskDO task, GrabDspOrderSnapshotDO snapshot, DspOrderRewardStrategyDO rewardStrategy);

    DriverOrderVO createForDelay(DspModelVO dspModel, ScheduleTaskDO task);


    DriverOrderVO create(DspModelVO dspModel, TransportGroupVO transportGroup, ScheduleTaskDO task, VBKDriverGrabOrderDO vbkDriverGrabOrder, DspOrderRewardStrategyDO rewardStrategy);

    DriverOrderVO create(DspModelVO dspModel, SupplierVO supplier ,VehicleVO vehicleVO,ScheduleTaskDO task, Integer isSelfDriver, Integer checkCode);

    DriverOrderVO createForSaaS(DspModelVO dspModel, SupplierVO supplier ,ScheduleTaskDO task, Integer isSelfDriver,boolean newProcess, Integer checkCode);
    /**
     * 创建司机单
     * @param dspModel
     * @param rewardAmount
     * @return
     */
    DriverOrderVO createForRedispatch(DspModelVO dspModel, BigDecimal rewardAmount, RedispatchOrderVO redispatchOrderVO,SubSkuVO subSkuVO);

}
