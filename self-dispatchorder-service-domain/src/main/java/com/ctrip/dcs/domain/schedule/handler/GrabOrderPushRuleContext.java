package com.ctrip.dcs.domain.schedule.handler;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotTypeEnum;
import com.ctrip.igt.framework.common.spring.InstanceLocator;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class GrabOrderPushRuleContext implements InitializingBean {

    public static Map<GrabDspOrderSnapshotTypeEnum, GrabOrderPushRuleHandler> context = Maps.newHashMap();

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, GrabOrderPushRuleHandler> map = InstanceLocator.getBeansOfType(GrabOrderPushRuleHandler.class);
        for (GrabOrderPushRuleHandler handler : map.values()) {
            context.put(handler.type(), handler);
        }
    }

    public static GrabOrderPushRuleHandler get(GrabDspOrderSnapshotTypeEnum type) {
        GrabOrderPushRuleHandler handler = context.get(type);
        Assert.notNull(handler);
        return handler;
    }
}
