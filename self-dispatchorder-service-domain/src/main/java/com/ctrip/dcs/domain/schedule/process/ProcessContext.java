package com.ctrip.dcs.domain.schedule.process;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.igt.framework.common.spring.InstanceLocator;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.TimeUnit;

;

/**
 * <AUTHOR>
 */
@Component
public class ProcessContext implements InitializingBean {

    private static Map<DspType, Process> context = Maps.newHashMap();

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, Process> map = InstanceLocator.getBeansOfType(Process.class);
        for (Process process : map.values()) {
            Processor annotation = process.getClass().getAnnotation(Processor.class);
            context.put(annotation.type(), process);
        }
    }

    public static void execute(ScheduleTaskDO task, DspOrderVO order) {
        Process process = context.get(task.getSubSku().getDspType());
        Assert.notNull(process);
        Map<String, String> tags = Maps.newHashMap();
        tags.put("dspType", task.getSubSku().getDspType().name());
        tags.put("subSkuId", task.getSubSku().getSubSkuId().toString());
        // 执行次数埋点
        MetricsUtil.recordValue(MetricsConstants.PROCESS_EXECUTE_COUNT_KEY, tags);
        Stopwatch stopwatch = Stopwatch.createStarted();
        process.execute(task, order);
        // 执行耗时埋点
        MetricsUtil.recordTime(MetricsConstants.PROCESS_EXECUTE_TIME_KEY, stopwatch.elapsed(TimeUnit.MILLISECONDS), tags);
    }

}
