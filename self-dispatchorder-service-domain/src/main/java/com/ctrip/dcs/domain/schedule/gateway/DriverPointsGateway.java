package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.schedule.value.CityPointVO;
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface DriverPointsGateway {
    /**
     * 查询司机分总分接口
     */
    List<DriverPointsVO> batchQueryDriverTotalPoints(List<Long> driverIds);

    List<DriverPointsVO> batchQueryDriverTotalPointAndRank(List<Long> driverIds,Long cityId);

    CityPointVO queryCityPointsInfo(Integer cityId);

    Map<Long, String> queryDriverLevel(List<Long> driverIds, CategoryCodeEnum categoryCode);

    Map<Long, Integer> batchQueryDriverLevel(List<Long> driverIds, CategoryCodeEnum categoryCode);

    /**
     * 查询司导等级code
     * @param driverIds
     * @return
     */
    public Map<Long, Integer> batchQueryDriverGuideLevel(List<Long> driverIds);

    /**
     * 是否查询司导等级
     * @return
     */
    public boolean isQueryGuideLevel(String categoryCode);

    /**
     * 查询订单特殊时段分
     * @param orderId
     * @return
     */
    BigDecimal querySpecialTimePoint(String orderId);

    /**
     * 查询司导分
     * @param driverIds
     * @return
     */
    List<DriverPointsVO> batchQueryGuidePoints(List<Long> driverIds);
}
