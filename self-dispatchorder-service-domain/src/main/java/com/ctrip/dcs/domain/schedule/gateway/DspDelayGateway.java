package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.value.DelayOrderVO;
import com.ctrip.dcs.domain.schedule.value.carconfig.DelayDspConfigVO;

/**
 * <AUTHOR>
 */
public interface DspDelayGateway {

    /**
     * 延后派入池
     * @param order
     * @return
     */
    DelayOrderVO insert(DspOrderVO order, ScheduleTaskDO task);

    /**
     * 查询延后派配置
     * @param order
     * @return
     */
    DelayDspConfigVO queryDelayDspConfig(DspOrderVO order);
}
