package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.service.QueryDriverMileageProfitService;
import com.ctrip.dcs.domain.common.value.DriverMileageProfitVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ExpectDriverMileageProfitVisitor extends AbstractVisitor {

    private DspOrderVO order;

    private List<DriverVO> drivers;

    private QueryDriverMileageProfitService queryDriverMileageProfitService;

    public ExpectDriverMileageProfitVisitor(DspOrderVO order, List<DriverVO> drivers) {
        Assert.notNull(order);
        Assert.notEmpty(drivers);
        this.order = order;
        this.drivers = drivers;
        this.queryDriverMileageProfitService = getInstance(QueryDriverMileageProfitService.class);
    }

    public ExpectDriverMileageProfitVisitor(DspOrderVO order, List<DriverVO> drivers, QueryDriverMileageProfitService queryDriverMileageProfitService) {
        this.order = order;
        this.drivers = drivers;
        this.queryDriverMileageProfitService = queryDriverMileageProfitService;
    }

    @Override
    public void visit(SortContext context) {
        Map<Long, DriverMileageProfitVO> map = context.getExpectDriverMileageProfitMap();
        List<DriverVO> filter = filter(drivers, map.keySet());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        List<DriverMileageProfitVO> list = queryDriverMileageProfitService.queryExpectDriverMileageProfit(filter, order.getEstimatedUseTime());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (DriverMileageProfitVO profit : list) {
            map.put(profit.getDriverId(), profit);
        }
    }
}
