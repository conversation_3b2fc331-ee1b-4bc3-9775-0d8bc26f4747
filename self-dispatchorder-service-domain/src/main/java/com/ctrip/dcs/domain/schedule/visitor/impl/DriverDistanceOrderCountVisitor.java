package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.DriverOrderStatusEnum;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.dsporder.value.TakenType;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.igt.framework.common.spring.InstanceLocator;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class DriverDistanceOrderCountVisitor extends AbstractVisitor {

    List<DriverVO> driverList;
    LocalDate useDate;
    SelfOrderQueryGateway orderGateway;

    // only for prod env
    public DriverDistanceOrderCountVisitor(LocalDate useDate, List<DriverVO> driverList) {
        this(useDate, driverList, InstanceLocator.getInstance(SelfOrderQueryGateway.class));
    }

    // only for ut env
    protected DriverDistanceOrderCountVisitor(LocalDate useDate, List<DriverVO> driverList, SelfOrderQueryGateway orderGateway) {
        this.useDate = Assert.notNull(useDate);
        this.driverList = Assert.notEmpty(driverList);
        this.orderGateway = Assert.notNull(orderGateway);
    }

    @Override
    public void visit(CheckContext context) {
        List<Long> driverIds = Optional.ofNullable(driverList).orElse(Collections.emptyList()).stream().map(DriverVO::getDriverId).toList();
        List<CategoryCodeEnum> categoryList = List.of(CategoryCodeEnum.FROM_AIRPORT, CategoryCodeEnum.TO_AIRPORT, CategoryCodeEnum.FROM_STATION, CategoryCodeEnum.TO_STATION, CategoryCodeEnum.POINT_TO_POINT);
        List<DriverOrderStatusEnum> orderStateList = List.of(DriverOrderStatusEnum.CONFIRMED, DriverOrderStatusEnum.TAKING, DriverOrderStatusEnum.DEPART, DriverOrderStatusEnum.ARRIVE, DriverOrderStatusEnum.BEGIN, DriverOrderStatusEnum.END, DriverOrderStatusEnum.FINISH);
        List<TakenType> takenTypeList = TakenType.getNonManualTakenTypes();
        LocalDateTime useTimeRangeLeft = useDate.atTime(0, 0, 0);
        LocalDateTime useTimeRangeRight = useDate.atTime(23, 59, 59);
        Map<Long, Map<Integer, Long>> response = orderGateway.queryDriverDistanceOrderCount(driverIds, categoryList, orderStateList, takenTypeList, useTimeRangeLeft, useTimeRangeRight);
        context.getContext().getDriverDistanceOrderCountMap().putAll(response);
    }

}