package com.ctrip.dcs.domain.schedule.process.impl;

import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.enums.UdlEnum;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.util.CatUtil;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.DriverUdlVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.BroadcastEvent;
import com.ctrip.dcs.domain.schedule.factory.GrabOrderFactory;
import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway;
import com.ctrip.dcs.domain.schedule.process.Process;
import com.ctrip.dcs.domain.schedule.process.Processor;
import com.ctrip.dcs.domain.schedule.repository.BroadcastRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.service.RecommendService;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

;

/**
 * 系统播报
 * <AUTHOR>
 */
@Processor(value = "systemBroadcastProcess", type = DspType.SYSTEM_BROADCAST)
public class SystemBroadcastProcess extends BaseProcess implements Process {

    private static final Logger logger = LoggerFactory.getLogger(SystemBroadcastProcess.class);

    private static final Integer BROADCAST_DRIVER_LIMIT = 100;

    @Autowired
    private ScheduleTaskRepository taskRepository;

    @Autowired
    private RecommendService recommendService;

    @Autowired
    private CheckService checkService;

    @Autowired
    private MessageProviderService messageProducer;

    @Autowired
    @Qualifier("broadcastGrabConfig")
    private ConfigService broadcastGrabConfig;

    @Autowired
    private GrabOrderFactory grabOrderFactory;

    @Autowired
    private BroadcastRepository grabOrderRepository;
    @Resource
    QueryDriverService queryDriverService;
    @Resource
    SysSwitchConfigGateway sysSwitchConfigGateway;

    @Override
    public void execute(ScheduleTaskDO task, DspOrderVO order) {
        DuidVO duid = new DuidVO();
        List<DspModelVO> list = Lists.newArrayList();
        try {
            // 运力检查
            List<CheckModel> checkModels = checkService.check(new DspCheckCommand(order, task.getSubSku(), DuidVO.of(task)));
            List<DspModelVO> models = checkModels.stream().filter(CheckModel::isPass).map(CheckModel::getModel).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(models)) {
                logger.info("system broadcast process", "driver recommend null! task id:{}, dsp order id:{}", task.getTaskId(), task.getDspOrderId());
                return;
            }
            list = models;
            // duid
            duid = DuidVO.of(task);
            super.upLoadMeMetrics("sum", duid, list);
            List<DriverVO> drivers = models.stream().map(DspModelVO::getDriver).collect(Collectors.toList());
            // 创建播报订单
            List<GrabOrderDO> grabOrders = createGrabOrder(order, duid, task.getSubSku(), drivers);
            // 发送播报消息
            sendBroadcastMessage(duid, models);
            // 缓存失效消息
            super.sendGrabOrderExpireMessage(grabOrders);
        } catch (Exception e) {
            super.upLoadMeMetrics("error", duid, list);
            logger.error(e);
        } finally {
            completeTask(task, false);
        }
    }

    protected List<GrabOrderDO> createGrabOrder(DspOrderVO order, DuidVO duid, SubSkuVO subSku, List<DriverVO> drivers) {
        List<GrabOrderDO> grabOrders = grabOrderFactory.create(order, duid, subSku, drivers);
        grabOrderRepository.save(grabOrders);
        //抢单大厅和播报 详情信息
        //优化，根绝司机id和派发单id，获取全量抢单数据
        saveGrabOrderDetail(order.getDspOrderId(), drivers, subSku, duid, grabOrders);
        return grabOrders;
    }

    public void sendBroadcastMessage(DuidVO duid, List<DspModelVO> models) {
        if (!sysSwitchConfigGateway.getDriverQmqAddUcsSwitch()) {
            sendBroadcastMsg(duid, models, 0L);
            return;
        }
        AtomicLong delay = new AtomicLong(NumberUtils.LONG_ZERO);
        //先按照境内外进行司机分组
        Map<Boolean, List<DspModelVO>> groupDspModel = models.stream().collect(Collectors.partitioningBy(t -> UdlEnum.isSgp(t.getDriver().getUdl())));
        for (Map.Entry<Boolean, List<DspModelVO>> entry :groupDspModel.entrySet()) {
            List<DspModelVO> dspModelVOS = entry.getValue();
            if (CollectionUtils.isEmpty(dspModelVOS)) {
                continue;
            }
            //尽量取uid不为空的司机
            DspModelVO dspModelVO = dspModelVOS.stream().filter(t -> StringUtils.isNotBlank(t.getDriver().getUdl()) && StringUtils.isNotBlank(t.getDriver().getRequestFrom())).findFirst().orElse(dspModelVOS.get(0));
            String udl = dspModelVO.getDriver().getUdl();
            String requestFrom = dspModelVO.getDriver().getRequestFrom();
            if (StringUtils.isBlank(udl)) {
                logger.info("systemBroadcastProcess_send_broadcast_message", "default udl");
                udl = UdlEnum.SHA.getValue();
                requestFrom = UdlEnum.SHA.getValue().split("_")[1];
            }
            long delayVal = CatUtil.doWithUdlOverride(() -> sendBroadcastMsg(duid, dspModelVOS, delay.get()), udl, requestFrom);
            delay.set(delayVal);
        }
    }
    
    public long sendBroadcastMsg(DuidVO duid, List<DspModelVO> models, long delay) {
        Integer limit = broadcastGrabConfig.getInteger(ConfigKey.BROADCAST_DRIVER_LIMIT_KEY, BROADCAST_DRIVER_LIMIT);
        List<List<DspModelVO>> partition = Lists.partition(models, limit);
        for (List<DspModelVO> list : partition) {
            messageProducer.send(new BroadcastEvent(duid, list, delay));
            super.upLoadMeMetrics("success", duid, list);
            delay += broadcastGrabConfig.getLong(ConfigKey.BROADCAST_DRIVER_DELAY_KEY, NumberUtils.LONG_ZERO);
        }
        return delay;
    }
}
