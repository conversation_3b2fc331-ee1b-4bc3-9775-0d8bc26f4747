package com.ctrip.dcs.domain.schedule.visitor.impl;

import com.ctrip.dcs.domain.common.service.QueryOrderDispatchModifyInfoService;
import com.ctrip.dcs.domain.common.util.LocalCollectionUtils;
import com.ctrip.dcs.domain.schedule.check.CheckContext;

import java.util.List;

/**
 * 查询改派屏蔽所需的数据
 */
public class PassengerDispatchModifyInfoVisitor extends AbstractVisitor{
    private String userOrderId;
    private QueryOrderDispatchModifyInfoService queryService;
    public PassengerDispatchModifyInfoVisitor(String userOrderId){
        this.userOrderId = userOrderId;
        this.queryService = getInstance(QueryOrderDispatchModifyInfoService.class);
    }

    public PassengerDispatchModifyInfoVisitor(String userOrderId, QueryOrderDispatchModifyInfoService queryService) {
        this.userOrderId = userOrderId;
        this.queryService = queryService;
    }

    @Override
    public void visit(CheckContext context) {
        //查询改派过的司机id集合
        List<String> driverIds = queryService.queryOrderDspModifyDriver(userOrderId);
        if(!LocalCollectionUtils.isEmpty(driverIds)){
            context.getOrderDispatchModifyDriverIds().addAll(driverIds);
        }
        //查询改派过的供应商id集合
        List<String> supplierIds = queryService.queryOrderDspModifySupplier(userOrderId);
        if(!LocalCollectionUtils.isEmpty(supplierIds)){
            context.getOrderDispatchModifySupplierIds().addAll(supplierIds);
        }
    }
}
