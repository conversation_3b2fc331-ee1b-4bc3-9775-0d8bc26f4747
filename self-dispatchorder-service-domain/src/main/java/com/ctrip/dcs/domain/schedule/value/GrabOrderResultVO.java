package com.ctrip.dcs.domain.schedule.value;

import com.ctrip.dcs.domain.common.enums.GrabOrderCode;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
public class GrabOrderResultVO {

    private GrabOrderDO grabOrder;

    private GrabDspOrderDriverIndexDO index;

    @Setter
    private GrabOrderCode code;

    @Setter
    private String resultCode;

    public GrabOrderResultVO(GrabOrderDO grabOrder) {
        this.grabOrder = grabOrder;
        this.code = GrabOrderCode.ORDER_TAKEN_FAIL;
    }

    public GrabOrderResultVO(GrabDspOrderDriverIndexDO index) {
        this.index = index;
        this.resultCode = GrabOrderCode.ORDER_TAKEN_FAIL.getCode();
    }
}
