package com.ctrip.dcs.domain.schedule.sort.hierarchical;

import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.category.CategoryManager;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.grade.GradeCalculator;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.monitor.SortingMonitor;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;

/**
 * 分层排序工厂类
 * 
 * 负责创建和管理分层排序系统的各个组件：
 * - HierarchicalSorter: 主排序器
 * - CategoryManager: 类别管理器
 * - GradeCalculator: 等级计算器
 * - HierarchicalSortConfig: 配置管理器
 * - SortingMonitor: 监控器
 * 
 * 提供单例模式和组件生命周期管理
 * 
 * <AUTHOR>
 */
public class HierarchicalSortFactory {

    private static final Logger logger = LoggerFactory.getLogger(HierarchicalSortFactory.class);

    private final ConfigService configService;
    
    // 组件实例 (单例)
    private volatile HierarchicalSortConfig config;
    private volatile CategoryManager categoryManager;
    private volatile GradeCalculator gradeCalculator;
    private volatile SortingMonitor monitor;
    private volatile HierarchicalSorter hierarchicalSorter;

    public HierarchicalSortFactory(ConfigService configService) {
        this.configService = configService;
    }

    /**
     * 获取分层排序器实例
     * 
     * @return 分层排序器
     */
    public HierarchicalSorter getHierarchicalSorter() {
        if (hierarchicalSorter == null) {
            synchronized (this) {
                if (hierarchicalSorter == null) {
                    hierarchicalSorter = createHierarchicalSorter();
                }
            }
        }
        return hierarchicalSorter;
    }

    /**
     * 获取配置管理器实例
     * 
     * @return 配置管理器
     */
    public HierarchicalSortConfig getConfig() {
        if (config == null) {
            synchronized (this) {
                if (config == null) {
                    config = createConfig();
                }
            }
        }
        return config;
    }

    /**
     * 获取类别管理器实例
     * 
     * @return 类别管理器
     */
    public CategoryManager getCategoryManager() {
        if (categoryManager == null) {
            synchronized (this) {
                if (categoryManager == null) {
                    categoryManager = createCategoryManager();
                }
            }
        }
        return categoryManager;
    }

    /**
     * 获取等级计算器实例
     * 
     * @return 等级计算器
     */
    public GradeCalculator getGradeCalculator() {
        if (gradeCalculator == null) {
            synchronized (this) {
                if (gradeCalculator == null) {
                    gradeCalculator = createGradeCalculator();
                }
            }
        }
        return gradeCalculator;
    }

    /**
     * 获取监控器实例
     * 
     * @return 监控器
     */
    public SortingMonitor getMonitor() {
        if (monitor == null) {
            synchronized (this) {
                if (monitor == null) {
                    monitor = createMonitor();
                }
            }
        }
        return monitor;
    }

    /**
     * 检查分层排序是否启用
     * 
     * @return 是否启用
     */
    public boolean isHierarchicalSortEnabled() {
        try {
            return getConfig().isHierarchicalSortEnabled();
        } catch (Exception e) {
            logger.error("Failed to check if hierarchical sort is enabled", e);
            return false; // 默认禁用
        }
    }

    /**
     * 检查系统健康度
     * 
     * @return 健康度分数 [0-100]
     */
    public double getSystemHealthScore() {
        try {
            return getMonitor().getSystemHealthScore();
        } catch (Exception e) {
            logger.error("Failed to get system health score", e);
            return 0.0;
        }
    }

    /**
     * 获取监控统计信息
     * 
     * @return 统计信息
     */
    public java.util.Map<String, Object> getMonitoringStats() {
        try {
            return getMonitor().getMonitoringStats();
        } catch (Exception e) {
            logger.error("Failed to get monitoring stats", e);
            return new java.util.HashMap<>();
        }
    }

    // ========== 私有创建方法 ==========

    /**
     * 创建分层排序器
     */
    private HierarchicalSorter createHierarchicalSorter() {
        try {
            logger.info("Creating HierarchicalSorter instance");
            
            HierarchicalSortConfig config = getConfig();
            CategoryManager categoryManager = getCategoryManager();
            GradeCalculator gradeCalculator = getGradeCalculator();
            SortingMonitor monitor = getMonitor();
            
            HierarchicalSorter sorter = new HierarchicalSorter(
                categoryManager, 
                gradeCalculator, 
                config, 
                monitor
            );
            
            logger.info("HierarchicalSorter created successfully");
            return sorter;
            
        } catch (Exception e) {
            logger.error("Failed to create HierarchicalSorter", e);
            throw new RuntimeException("Failed to create HierarchicalSorter", e);
        }
    }

    /**
     * 创建配置管理器
     */
    private HierarchicalSortConfig createConfig() {
        try {
            logger.info("Creating HierarchicalSortConfig instance");
            
            HierarchicalSortConfig config = new HierarchicalSortConfig(configService);
            
            // 验证配置
            if (!config.validateConfig()) {
                logger.warn("HierarchicalSortConfig validation failed, but continuing with default values");
            }
            
            logger.info("HierarchicalSortConfig created successfully");
            return config;
            
        } catch (Exception e) {
            logger.error("Failed to create HierarchicalSortConfig", e);
            throw new RuntimeException("Failed to create HierarchicalSortConfig", e);
        }
    }

    /**
     * 创建类别管理器
     */
    private CategoryManager createCategoryManager() {
        try {
            logger.info("Creating CategoryManager instance");
            
            HierarchicalSortConfig config = getConfig();
            CategoryManager manager = new CategoryManager(config);
            
            logger.info("CategoryManager created successfully");
            return manager;
            
        } catch (Exception e) {
            logger.error("Failed to create CategoryManager", e);
            throw new RuntimeException("Failed to create CategoryManager", e);
        }
    }

    /**
     * 创建等级计算器
     */
    private GradeCalculator createGradeCalculator() {
        try {
            logger.info("Creating GradeCalculator instance");
            
            GradeCalculator calculator = new GradeCalculator();
            
            logger.info("GradeCalculator created successfully");
            return calculator;
            
        } catch (Exception e) {
            logger.error("Failed to create GradeCalculator", e);
            throw new RuntimeException("Failed to create GradeCalculator", e);
        }
    }

    /**
     * 创建监控器
     */
    private SortingMonitor createMonitor() {
        try {
            logger.info("Creating SortingMonitor instance");
            
            SortingMonitor monitor = new SortingMonitor();
            
            logger.info("SortingMonitor created successfully");
            return monitor;
            
        } catch (Exception e) {
            logger.error("Failed to create SortingMonitor", e);
            throw new RuntimeException("Failed to create SortingMonitor", e);
        }
    }

    // ========== 生命周期管理 ==========

    /**
     * 重新加载配置 (用于配置热更新)
     */
    public void reloadConfig() {
        synchronized (this) {
            logger.info("Reloading hierarchical sort configuration");
            
            try {
                // 重新创建配置
                config = createConfig();
                
                // 重新创建依赖配置的组件
                categoryManager = createCategoryManager();
                hierarchicalSorter = createHierarchicalSorter();
                
                logger.info("Hierarchical sort configuration reloaded successfully");
                
            } catch (Exception e) {
                logger.error("Failed to reload hierarchical sort configuration", e);
                throw new RuntimeException("Failed to reload configuration", e);
            }
        }
    }

    /**
     * 重置监控计数器
     */
    public void resetMonitoringCounters() {
        try {
            getMonitor().resetCounters();
            logger.info("Monitoring counters reset successfully");
        } catch (Exception e) {
            logger.error("Failed to reset monitoring counters", e);
        }
    }

    /**
     * 销毁所有组件 (用于系统关闭)
     */
    public void destroy() {
        synchronized (this) {
            logger.info("Destroying HierarchicalSortFactory components");
            
            hierarchicalSorter = null;
            categoryManager = null;
            gradeCalculator = null;
            config = null;
            monitor = null;
            
            logger.info("HierarchicalSortFactory components destroyed");
        }
    }

    // ========== 诊断方法 ==========

    /**
     * 执行系统诊断
     * 
     * @return 诊断结果
     */
    public java.util.Map<String, Object> performDiagnostics() {
        java.util.Map<String, Object> diagnostics = new java.util.HashMap<>();
        
        try {
            // 检查组件状态
            diagnostics.put("hierarchical_sort_enabled", isHierarchicalSortEnabled());
            diagnostics.put("config_valid", getConfig().validateConfig());
            diagnostics.put("system_health_score", getSystemHealthScore());
            
            // 检查配置
            diagnostics.put("category_weights", getConfig().getAllCategoryWeights());
            diagnostics.put("overall_grade_strategy", getConfig().getOverallGradeStrategy());
            diagnostics.put("fallback_threshold", getConfig().getFallbackThreshold());
            
            // 监控统计
            diagnostics.put("monitoring_stats", getMonitoringStats());
            
            diagnostics.put("diagnosis_time", System.currentTimeMillis());
            diagnostics.put("diagnosis_status", "SUCCESS");
            
        } catch (Exception e) {
            logger.error("Failed to perform diagnostics", e);
            diagnostics.put("diagnosis_status", "FAILED");
            diagnostics.put("diagnosis_error", e.getMessage());
        }
        
        return diagnostics;
    }
}
