package com.ctrip.dcs.domain.schedule.repository;

import com.ctrip.dcs.domain.schedule.ScheduleDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ScheduleRepository {

    ScheduleDO find(Long scheduleId);

    void save(ScheduleDO schedule);

    void execute(ScheduleDO schedule);

    void shutdown(ScheduleDO schedule);

    List<ScheduleDO> queryBefore(Date beginTime, Date endTime);

    List<ScheduleDO> query(String dspOrderId);
}
