package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.common.enums.DspStage;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.context.DspContext;
import com.ctrip.dcs.domain.schedule.value.OrderDisLimitVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2023/3/21 19:27
 */
public interface OrderDisLimitServiceGateway {

     Map<Long, OrderDisLimitVO> queryOrderDisLimitMap(List<CheckModel> models, DspOrderVO dspOrder, DspStage dspStage, DspType dspType, DspContext dspContext, <PERSON><PERSON><PERSON> duid);

}
