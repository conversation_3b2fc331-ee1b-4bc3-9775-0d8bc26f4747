package com.ctrip.dcs.domain.schedule.entity;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.math.Money;
import com.ctrip.dcs.domain.common.enums.ScheduleTaskStatus;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.value.RewardVO;
import com.ctrip.dcs.domain.schedule.value.ScheduleTaskExpectExecuteTimeVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Getter
public class ScheduleTaskDO {

    private static final Logger logger = LoggerFactory.getLogger(ScheduleTaskDO.class);

    private Long id;

    private Long taskId;

    private String scheduleTaskId;

    private Long scheduleId;

    private String dspOrderId;

    private Date executeTime;

    private SubSkuVO subSku;

    private ScheduleTaskStatus status;

    @Deprecated
    private Money reward;

    @Deprecated
    private Money vbkReward;

    private Integer begin;

    private Integer end;

    private Integer limit;

    private Integer priority;

    private Integer round;

    private long dspRewardStrategyId;

    private Date createTime;

    private Date cancelTime;

    @Builder
    public ScheduleTaskDO(Long id, Long taskId, String scheduleTaskId, Long scheduleId, String dspOrderId, Date executeTime, SubSkuVO subSku, ScheduleTaskStatus status, Money reward, Money vbkReward, Integer begin, Integer end, Integer limit, Integer priority, Integer round, Long dspRewardStrategyId, Date createTime, Date cancelTime) {
        Assert.notBlank(dspOrderId);
        Assert.notNull(subSku);
        this.id = id;
        this.taskId = taskId;
        this.scheduleTaskId = scheduleTaskId;
        this.scheduleId = scheduleId;
        this.dspOrderId = dspOrderId;
        this.executeTime = executeTime;
        this.subSku = subSku;
        this.status = status;
        this.reward = reward;
        this.vbkReward = vbkReward;
        this.begin = begin;
        this.end = end;
        this.limit = limit;
        this.priority = priority;
        this.round = round;
        this.dspRewardStrategyId = Optional.ofNullable(dspRewardStrategyId).orElse(NumberUtils.LONG_ZERO);
        this.createTime = createTime;
        this.cancelTime = cancelTime;
    }

    public void execute() {
        status = ScheduleTaskStatus.EXECUTE;
    }

    public void init() {
        status = ScheduleTaskStatus.INIT;
    }

    public void waitExecute(DspOrderVO dspOrder, RewardVO reward, Integer retry) {
        if (isCancel()) {
            logger.info("ScheduleTaskInfo", "task is cancel. task id : {}", this.getTaskId());
            // 任务已取消
            return;
        }
        ScheduleTaskExpectExecuteTimeVO expectExecuteTime = getExpectExecuteTime(dspOrder);
        if (expectExecuteTime.after(DateUtil.addSeconds(new Date(), retry))) {
            logger.info("ScheduleTaskInfo", "task expect execute time after now. task id : {}, expectExecuteTime : {}", this.getTaskId(), DateUtil.formatDate(expectExecuteTime.getDate(), DateUtil.DATETIME_FORMAT));
            return;
        }
        // 执行时间
        this.executeTime = expectExecuteTime.getDate();
        // 增加任务轮次
        this.round++;
        // 任务状态变更为待执行
        this.status = ScheduleTaskStatus.WAIT_EXECUTE;
        // 计算任务奖励金额
        this.reward = reward.reward(dspOrder, this);
        // 计算vbk奖励金额
        this.vbkReward = reward.vbkReward(this);
        // 加价策略id
        this.dspRewardStrategyId = reward.getDspRewardStrategyId(this);
    }

    public void cancel() {
        status = ScheduleTaskStatus.CANCEL;
        cancelTime = new Date();
    }

    public void complete() {
        status = ScheduleTaskStatus.COMPLETE;
    }

    public boolean isInit() {
        return Objects.equals(status, ScheduleTaskStatus.INIT);
    }

    public boolean isCancel() {
        return Objects.equals(status, ScheduleTaskStatus.CANCEL);
    }

    public boolean isWaitExecute() {
        return Objects.equals(status, ScheduleTaskStatus.WAIT_EXECUTE);
    }

    public boolean isExecute() {
        return Objects.equals(status, ScheduleTaskStatus.EXECUTE);
    }

    public boolean isTimeout() {
        if (subSku.getRetrySecond() == null || subSku.getRetrySecond() <= 0) {
            return true;
        }
        Long retry = this.subSku.getRoundRetrySecond(this.round);
        return DateUtil.seconds(executeTime, new Date()) > retry;
    }

    public ScheduleTaskExpectExecuteTimeVO  getExpectExecuteTime(DspOrderVO dspOrder) {
        Long retry = this.subSku.getRoundRetrySecond(this.round);
        logger.info("ScheduleTaskExpectExecuteTimeInfo", "task id : {}, retry second: {}", this.getTaskId(), retry);
        return new ScheduleTaskExpectExecuteTimeVO(this.status, this.createTime, this.begin, dspOrder.getLastConfirmTimeBj(), this.end, dspOrder.getEstimatedUseTimeBj(), this.limit, this.executeTime, retry);
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public void setDspRewardStrategyId(Long dspRewardStrategyId) {
        this.dspRewardStrategyId = dspRewardStrategyId;
    }

    public String getScheduleTaskId() {
        return StringUtils.isBlank(scheduleTaskId) ? String.valueOf(taskId) : scheduleTaskId;
    }
}
