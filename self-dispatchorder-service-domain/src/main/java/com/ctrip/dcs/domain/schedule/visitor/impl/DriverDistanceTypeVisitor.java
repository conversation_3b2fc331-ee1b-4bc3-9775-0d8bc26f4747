package com.ctrip.dcs.domain.schedule.visitor.impl;

import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class DriverDistanceTypeVisitor extends AbstractVisitor {

    List<DriverVO> driverList;

    public DriverDistanceTypeVisitor(List<DriverVO> driverList) {
        this.driverList = driverList;
    }

    @Override
    public void visit(CheckContext context) {
        // 1.获取所有司机和对应的运力组
        Map<Long, List<Long>> transportGroupIdMap = Optional.ofNullable(driverList).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(DriverVO::getDriverId, this::getTransportGroupIds));
        // 2.获取所有运力组ID列表
        List<Long> transportGroupIdList = transportGroupIdMap.values().stream().flatMap(List::stream).distinct().collect(Collectors.toList());
        // 3.查询所有运力组详情
        List<TransportGroupVO> transportGroupList = context.getContext().getService().queryTransportList(transportGroupIdList);
        // 4.将运力组列表转为MAP
        Map<Long, TransportGroupVO> transportGroupMap = transportGroupList.stream().collect(Collectors.toMap(TransportGroupVO::getTransportGroupId, Function.identity(), (v1, v2) -> v2));
        // 5.筛选出所有短公里司机
        Map<Long, Integer> shortDistanceDrivers = transportGroupIdMap.entrySet().stream().filter(isShortDistance(transportGroupMap)).collect(Collectors.toMap(Map.Entry::getKey, e -> YesOrNo.YES.getCode()));
        // 6.写入上下文
        context.getContext().getDriverDistanceTypeMap().putAll(shortDistanceDrivers);
    }

    protected Predicate<Map.Entry<Long, List<Long>>> isShortDistance(Map<Long, TransportGroupVO> transportGroupMap) {
        return e -> Optional.ofNullable(e)
                .map(Map.Entry::getValue) // 该司机的所有运力组ID
                .orElse(Collections.emptyList())
                .stream()
                .map(transportGroupMap::get) // 获取该运力组详情
                .map(TransportGroupVO::getShortDisSwitch) // 判断是否为短公里
                .filter(Objects::nonNull)
                .anyMatch(YesOrNo::isYes); // 该司机的任意一个运力组为短公里 则该司机为短公里司机
    }

    protected List<Long> getTransportGroupIds(DriverVO in) {
        return Optional.ofNullable(in)
                .map(DriverVO::getTransportGroups)
                .orElse(Collections.emptyList())
                .stream()
                .map(TransportGroupVO::getTransportGroupId)
                .collect(Collectors.toList());
    }

}