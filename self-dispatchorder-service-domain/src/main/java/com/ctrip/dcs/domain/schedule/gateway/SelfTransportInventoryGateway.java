package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.dsporder.value.UseDays;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface SelfTransportInventoryGateway {

    /**
     * @param categoryCode not null
     * @param estimatedUseTimeBj
     * @param useDays
     * @param cityId
     * @param locationCode
     * @param transportGroupIds
     * @return
     */
    Map<Long, Integer> queryTransportGroupUsableInventory(CategoryCodeEnum categoryCode, Date estimatedUseTimeBj, UseDays useDays, Integer cityId, String locationCode, List<Long> transportGroupIds, Boolean isCharteredRouteOrder, Boolean isNearOrder
    , DspOrderVO dspOrder);

    Long useRatePlanInventory(Integer cityId, String categoryCode,Integer useDays,List<Long> shuntSupplierIds,Long ratePlanId, String userOrderId,String dspOrderId,Long skuId);
}

