package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderChangeDriverRecordRepository;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import org.joda.time.LocalDate;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> liu
 */
public class DriverChangeVisitor extends AbstractVisitor {

    private final Long originDriverId;

    private final List<DriverVO> drivers;

    private final DspOrderChangeDriverRecordRepository dspOrderChangeDriverRecordRepository;

    public DriverChangeVisitor(Long originDriverId, List<DriverVO> drivers) {
        Assert.notEquals(originDriverId, 0L);
        this.originDriverId = originDriverId;
        this.drivers = drivers;
        this.dspOrderChangeDriverRecordRepository = getInstance(DspOrderChangeDriverRecordRepository.class);
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, Integer> driverChangeOutLimitMap = context.getDriverChangeOutCountMap();
        Map<Long, Integer> driverChangeInLimitMap = context.getDriverChangeInCountMap();
        // 当前时间
        LocalDate operateTime = new LocalDate();
        // 读取DB 记录原司机更改至其他司机的次数
        try{
            int fromDrvChangeTimes = dspOrderChangeDriverRecordRepository.selectCountByFromDrvId(originDriverId.toString(), operateTime);
            driverChangeOutLimitMap.put(originDriverId, fromDrvChangeTimes);
        } catch (Exception e) {
            driverChangeOutLimitMap.put(originDriverId, 0);
        }

        for( DriverVO driver : drivers) {
            // 待检查的司机id
            Long toDrvId = driver.getDriverId();
            try{
                int toDrvDrvChangeTimes = dspOrderChangeDriverRecordRepository.selectCountByToDrvId(toDrvId.toString(), operateTime);
                // 记录可用司机已经更改次数
                driverChangeInLimitMap.put(toDrvId, toDrvDrvChangeTimes);
            } catch (Exception e) {
                driverChangeInLimitMap.put(toDrvId, 0);
            }
        }
    }
}
