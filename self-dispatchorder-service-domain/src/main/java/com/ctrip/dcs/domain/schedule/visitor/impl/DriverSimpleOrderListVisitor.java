package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DriverWorkTimeVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 查询司机工作时间范围内的订单列表
 * 返回字段简化，使用之前需确认数据源赋值
 * 支持排除司机单
 */
public class DriverSimpleOrderListVisitor extends AbstractVisitor {

    private static final Logger logger = LoggerFactory.getLogger(DriverSimpleOrderListVisitor.class);

    // 230~700
    private static final List<OrderStatusEnum> ORDER_STATUS_LIST = Lists.newArrayList(
            OrderStatusEnum.DRIVER_CONFIRMED,
            OrderStatusEnum.DRIVER_CAR_CONFIRMED,
            OrderStatusEnum.DRIVER_TO_MEET,
            OrderStatusEnum.DRIVER_ARRIVE,
            OrderStatusEnum.DRIVER_SERVICE_START,
            OrderStatusEnum.DRIVER_SERVICE_END,
            OrderStatusEnum.ORDER_FINISH);

    private DspOrderVO order;

    private List<DriverVO> drivers;

    private QueryDspOrderService queryDspOrderService;

    private boolean cache;

    public DriverSimpleOrderListVisitor(DspOrderVO order, List<DriverVO> drivers, boolean cache) {
        Assert.notNull(order);
        Assert.notEmpty(drivers);
        this.order = order;
        this.drivers = drivers;
        this.queryDspOrderService = getInstance("queryDspOrderCacheServiceImpl", QueryDspOrderService.class);
        this.cache = cache;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, List<DspOrderVO>> orderListMap = context.getContext().getOrderListMap();
        visit(orderListMap);
    }

    @Override
    public void visit(SortContext context) {
        Map<Long, List<DspOrderVO>> orderListMap = context.getDspContext().getOrderListMap();
        visit(orderListMap);
    }

    private void visit(Map<Long, List<DspOrderVO>> orderListMap) {
        List<DriverVO> filter = filter(drivers, orderListMap.keySet());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        Map<Long, List<DspOrderVO>> result = queryOrderList(order, filter, cache);
        orderListMap.putAll(result);
    }

    private Map<Long, List<DspOrderVO>> queryOrderList(DspOrderVO dspOrder, List<DriverVO> drivers, boolean cache) {
        Map<Long, DriverWorkTimeVO> workTimeMap = Maps.newHashMap();
        Date begin = null;
        Date end = null;
        for (DriverVO driver : drivers) {
            DriverWorkTimeVO workTime = driver.findHitWorkTime(dspOrder);
            if (Objects.isNull(workTime)) {
                continue;
            }
            workTimeMap.put(driver.getDriverId(), workTime);
            if (begin == null || workTime.getStart().before(begin)) {
                begin = workTime.getStart();
            }
            if (end == null || workTime.getEnd().after(end)) {
                end = workTime.getEnd();
            }
        }
        if (Objects.isNull(begin) || Objects.isNull(end)) {
            return Collections.emptyMap();
        }
        // 司机工作时间前后加buffer
        begin = DateUtil.addHours(begin, -2);
        end = DateUtil.addHours(end, 2);
        Map<Long, List<DspOrderVO>> orderMap = queryDspOrderService.querySimpleOrderList(drivers, begin, end, ORDER_STATUS_LIST, cache);
        if (MapUtils.isEmpty(orderMap)) {
            return Collections.emptyMap();
        }
        Map<Long, List<DspOrderVO>> result = Maps.newHashMap();
        orderMap.forEach((driverId, orders) -> {
            DriverWorkTimeVO driverWorkTime = workTimeMap.get(driverId);
            if (Objects.isNull(driverWorkTime)) {
                return;
            }
            List<DspOrderVO> list = orders.stream()
                    .filter(o -> !o.getDspOrderId().equals(order.getOriginalDspOrderId())) // 忽略原单
                    .filter(o -> driverWorkTime.contain(o.getEstimatedUseTime(), true)).collect(Collectors.toList());
            result.put(driverId, list);
        });
        logger.info("querySimpleOrderList2OrderListMap", JacksonSerializer.INSTANCE().serialize(result));
        return result;
    }
}
