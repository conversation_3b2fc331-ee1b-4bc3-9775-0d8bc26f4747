package com.ctrip.dcs.domain.schedule.visitor.impl;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.ConflictGateway;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DriverOrderTimeConflictVisitor extends AbstractVisitor {

    private DspOrderVO dspOrder;

    private List<DriverVO> drivers;

    private ConflictGateway conflictGateway;

    public DriverOrderTimeConflictVisitor() {
    }

    public DriverOrderTimeConflictVisitor(DspOrderVO dspOrder, List<DriverVO> drivers) {
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.conflictGateway = getInstance(ConflictGateway.class);
    }

    public DriverOrderTimeConflictVisitor(DspOrderVO dspOrder, List<DriverVO> drivers, ConflictGateway conflictGateway) {
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.conflictGateway = conflictGateway;
    }

    @Override
    public void visit(CheckContext context) {
        if (Objects.isNull(dspOrder) || CollectionUtils.isEmpty(drivers)) {
            return;
        }
        List<Long> driverIds = drivers.stream()
                .map(DriverVO::getDriverId)
                .filter(id -> !context.getTimeConflictDriverIds().contains(id))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(driverIds)) {
            return;
        }
        List<Long> conflictDriverIds = conflictGateway.checkDriverOrderTimeConflict(dspOrder, driverIds);
        if (CollectionUtils.isNotEmpty(conflictDriverIds)) {
            context.getTimeConflictDriverIds().addAll(conflictDriverIds);
        }
    }
}
