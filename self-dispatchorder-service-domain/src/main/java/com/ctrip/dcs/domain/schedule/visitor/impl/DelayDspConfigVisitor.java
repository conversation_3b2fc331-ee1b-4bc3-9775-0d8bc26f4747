package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.DspDelayGateway;
import com.ctrip.dcs.domain.schedule.value.carconfig.DelayDspConfigVO;

import java.util.Map;
import java.util.Objects;

public class DelayDspConfigVisitor extends AbstractVisitor {

    private DspOrderVO order;

    private DspDelayGateway dspDelayGateway;

    public DelayDspConfigVisitor(DspOrderVO order) {
        Assert.notNull(order);
        this.order = order;
        this.dspDelayGateway = getInstance(DspDelayGateway.class);
    }

    public DelayDspConfigVisitor(DspOrderVO order, DspDelayGateway gateway) {
        Assert.notNull(order);
        this.order = order;
        this.dspDelayGateway = gateway;
    }

    @Override
    public void visit(CheckContext context) {
        Map<String, DelayDspConfigVO> map = context.getDelayDspConfigMap();
        if (map.containsKey(order.getDspOrderId())) {
            return;
        }
        DelayDspConfigVO delayDspConfigVO = dspDelayGateway.queryDelayDspConfig(order);
        if (Objects.nonNull(delayDspConfigVO)) {
            map.put(order.getDspOrderId(), delayDspConfigVO);
        }
    }
}
