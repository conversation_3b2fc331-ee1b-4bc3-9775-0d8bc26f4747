package com.ctrip.dcs.domain.schedule.factory;


import com.ctrip.dcs.domain.common.enums.ScheduleType;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.ScheduleDO;

;

/**
 * 调度工厂
 * <AUTHOR>
 */
public interface ScheduleFactory {

    /**
     * 创建调度
     * @param order
     * @return
     */
    ScheduleDO create(DspOrderVO order);

    ScheduleDO create(DspOrderVO order, ScheduleType scheduleType, Long strategyId);
}
