package com.ctrip.dcs.domain.schedule.repository;

import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */public interface GrabDspOrderDriverIndexRepository {

     List<GrabDspOrderDriverIndexDO> query(String orderId);

     List<GrabDspOrderDriverIndexDO> queryFomCache(String orderId);

     List<GrabDspOrderDriverIndexDO> query(String orderId, List<Long> driverIds);

    Long queryCountBroadcastPushTime(Date startTime, Date endTime);

    List<GrabDspOrderDriverIndexDO> queryBroadcastPushTime(Date startTime, Date endTime, Integer page, Integer size);

    Long queryCountGrabPushTime(Date startTime, Date endTime);

    List<GrabDspOrderDriverIndexDO> queryGrabPushTime(Date startTime, Date endTime, Integer page, Integer size);

    void save(List<GrabDspOrderDriverIndexDO> indexes);

    void update(List<GrabDspOrderDriverIndexDO> indexes);

    List<GrabDspOrderDriverIndexDO> query(Long driverId, String categoryCode);

    List<GrabDspOrderDriverIndexDO> query(Long driverId, Date beginTime, Date endTime, String categoryCode);

    List<GrabDspOrderDriverIndexDO> querySubmitIndex(String dspOrderId, Date submitTime);

    void updateBroadcastPushTime(List<GrabDspOrderDriverIndexDO> indexes);

    void updateSubmitDuid(List<GrabDspOrderDriverIndexDO> indexes);
}
