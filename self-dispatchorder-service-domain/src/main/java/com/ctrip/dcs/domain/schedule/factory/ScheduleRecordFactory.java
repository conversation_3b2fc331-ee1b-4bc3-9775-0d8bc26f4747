package com.ctrip.dcs.domain.schedule.factory;

import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.value.ScheduleRecordVO;
import com.ctrip.dcs.domain.schedule.value.ScheduleTaskRecordVO;

import java.util.List;

/**
 * 调度记录工厂
 * <AUTHOR>
 */
public interface ScheduleRecordFactory {

    /**
     * 调度记录
     * @param schedule
     * @param event
     * @return
     */
    ScheduleRecordVO create(ScheduleDO schedule, ScheduleEventType event);

    /**
     * 调度任务记录
     * @param tasks
     * @return
     */
    List<ScheduleTaskRecordVO> create(List<ScheduleTaskDO> tasks);
}
