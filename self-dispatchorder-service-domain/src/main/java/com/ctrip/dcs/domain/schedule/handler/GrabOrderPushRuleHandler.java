package com.ctrip.dcs.domain.schedule.handler;

import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotTypeEnum;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.entity.DspOrderRewardStrategyDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface GrabOrderPushRuleHandler {

    Boolean handle(GrabDspOrderSnapshotDO snapshot, List<GrabDspOrderDriverIndexDO> indexes);

    GrabDspOrderSnapshotTypeEnum type();
}
