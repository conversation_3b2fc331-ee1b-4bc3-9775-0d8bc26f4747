package com.ctrip.dcs.domain.schedule.process.impl;

import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.exception.OrderStatusException;
import com.ctrip.dcs.domain.common.service.*;
import com.ctrip.dcs.domain.common.util.CatUtil;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.OrderExtendInfoUtil;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.dsporder.factory.BatchConfirmDspOrderFactory;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.dcs.domain.schedule.check.CheckCode;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand;
import com.ctrip.dcs.domain.schedule.entity.DspOrderRewardStrategyDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.GrabOrderFailEvent;
import com.ctrip.dcs.domain.schedule.event.GuideGrabOrderFailEvent;
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory;
import com.ctrip.dcs.domain.schedule.gateway.ConflictGateway;
import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway;
import com.ctrip.dcs.domain.schedule.repository.DspOrderRewardStrategyRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.service.RecommendService;
import com.ctrip.dcs.domain.schedule.service.SortService;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.GrabOrderResultVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class SelectGrabBroadcastGrabProcess extends BaseProcess {

    private static final Logger logger = LoggerFactory.getLogger(SelectGrabBroadcastGrabProcess.class);

    @Autowired
    private ScheduleTaskRepository scheduleTaskRepository;

    @Autowired
    private SortService sortService;

    @Autowired
    private CheckService checkService;

    @Autowired
    private DriverOrderFactory driverOrderFactory;

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    @Qualifier("broadcastGrabConfig")
    private ConfigService broadcastGrabConfig;

    @Autowired
    @Qualifier("broadcastGrabFailConfig")
    private ConfigService broadcastGrabFailConfig;
    @Autowired
    private QueryTransportGroupService queryTransportGroupService;

    @Autowired
    private GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository;

    @Autowired
    private GrabDspOrderSnapshotRepository grabDspOrderSnapshotRepository;

    @Autowired
    private SelectVBKGrabOrderProcess selectVBKGrabOrderProcess;

    @Autowired
    private DspOrderRewardStrategyRepository dspOrderRewardStrategyRepository;
    
    @Resource
    private SysSwitchConfigGateway sysSwitchConfigGateway;

    public void execute(DspOrderVO dspOrder, SubSkuVO subSku) {
        // 查询抢单快照
        GrabDspOrderSnapshotDO snapshot = grabDspOrderSnapshotRepository.query(dspOrder.getDspOrderId());
        // 查询提交抢单的映射
        List<GrabDspOrderDriverIndexDO> indexes = querySubmitIndex(snapshot);
        List<GrabOrderResultVO> results = indexes.stream().map(GrabOrderResultVO::new).collect(Collectors.toList());
        // 校验
        boolean ok = validate(dspOrder, snapshot, indexes);
        if (!ok) {
            logger.info("SelectBroadcastGrabProcess", "validate fail, dspOrderId:{}", dspOrder.getDspOrderId());
            // 抢单失败消息
            notice(dspOrder, results);
            return;
        }
        try {
            Set<Long> driverIds = indexes.stream().map(GrabDspOrderDriverIndexDO::getDriverId).collect(Collectors.toSet());
            logger.info("SelectBroadcastGrabProcess", "dsp order id:{}, driver ids:{}", dspOrder.getDspOrderId(), JsonUtils.toJson(driverIds));
            // 查询司机
            Long supplierId = dspOrder.getSupplierId() != null ? dspOrder.getSupplierId().longValue() : null;
            List<DriverVO> drivers = queryDriverService.queryDriver(driverIds, CategoryUtils.selfGetParentType(dspOrder), supplierId);
            // 排序
            drivers = sortService.sort(dspOrder, subSku, drivers);
            Map<Long /* driver id */, GrabOrderResultVO> map = results.stream().collect(Collectors.toMap(o -> o.getIndex().getDriverId(), o -> o, (o1, o2) -> o2));
            for (DriverVO driver : drivers) {
                try {
                    GrabOrderResultVO result = map.get(driver.getDriverId());
                    if (Objects.nonNull(result) && Objects.nonNull(result.getIndex())) {
                        GrabDspOrderDriverIndexDO index = result.getIndex();
                        // 订单接单检查
                        CheckModel checkModel = checkService.check(new TakenCheckCommand(dspOrder, subSku, driver, DuidVO.of(index.getSubmitDuid())));
                        if (!checkModel.isPass()) {
                            logger.info("SelectBroadcastGrabProcess", "driver taken check fail! dsp order id:{}, driver id:{}", index.getDspOrderId(), driver.getDriverId());
                            String code = getGrabOrderCode(checkModel.getCheckCode());
                            result.setResultCode(code);
                            continue;
                        }
                        // 派发任务
                        ScheduleTaskDO scheduleTask = scheduleTaskRepository.query(index.getSubmitDuid());
                        if (scheduleTask != null) {
                            // 查询司机车辆信息
                            VehicleVO vehicle = selectVBKGrabOrderProcess.queryVehicle(driver,CategoryUtils.selfGetParentType(dspOrder));
                            // 查询订单运力组
                            TransportGroupVO transportGroup = queryTransportGroup(dspOrder, checkModel, snapshot);
                            DspOrderRewardStrategyDO rewardStrategy = null;
                            if (Objects.nonNull(scheduleTask.getDspRewardStrategyId()) && scheduleTask.getDspRewardStrategyId() > 0L) {
                                // 奖励金额和币种
                                rewardStrategy = dspOrderRewardStrategyRepository.find(scheduleTask.getDspRewardStrategyId());
                            }
                            // 下司机单
                            DriverOrderVO driverOrder = createDriverOrder(checkModel.getModel(), transportGroup, scheduleTask, snapshot, rewardStrategy);
                            if (Objects.isNull(driverOrder)) {
                                continue;
                            }
                            // 应单
                            String code = confirmOrder(dspOrder, driver, vehicle, transportGroup, driverOrder, scheduleTask, snapshot);
                            result.setResultCode(code);
                            if (GrabOrderCode.isSuccess(code)) {
                                // 司机应单成功
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.warn(e);
                }
            }
        } catch (Exception e) {
            logger.error(e);
        } finally {
            // 通知抢单结果
            notice(dspOrder, results);
        }
    }

    public boolean validate(DspOrderVO dspOrder, GrabDspOrderSnapshotDO snapshot, List<GrabDspOrderDriverIndexDO> indexes) {
        Integer orderStatus = Optional.ofNullable(dspOrder).map(DspOrderVO::getOrderStatus).orElse(null);
        Boolean isVbkGrabType = Optional.ofNullable(snapshot).map(GrabDspOrderSnapshotDO::getGrabType).map(GrabDspOrderSnapshotTypeEnum::isVBK).orElse(false);
        boolean validateOrderStatus = isVbkGrabType ? Objects.equals(orderStatus, OrderStatusEnum.DISPATCH_CONFIRMED.getCode()) : Objects.equals(orderStatus, OrderStatusEnum.TO_BE_CONFIRMED.getCode());
        return validateOrderStatus && CollectionUtils.isNotEmpty(indexes);
    }

    public List<GrabDspOrderDriverIndexDO> querySubmitIndex(GrabDspOrderSnapshotDO snapshot) {
        // 多留1秒buffer，防止延迟导致拉取不到
        Date submitTime = DateUtil.addSeconds(new Date(), -snapshot.getTipsDelaySecond() - 5);
        return grabDspOrderDriverIndexRepository.querySubmitIndex(snapshot.getDspOrderId(), submitTime);
    }

    public TransportGroupVO queryTransportGroup(DspOrderVO dspOrder, CheckModel checkModel, GrabDspOrderSnapshotDO snapshot) {
        return snapshot.getGrabType().isVBK() ? queryTransportGroupService.queryTransportGroup(dspOrder.getTransportGroupId()) : checkModel.getModel().getTransportGroup();
    }

    public DriverOrderVO createDriverOrder(DspModelVO model, TransportGroupVO transportGroup, ScheduleTaskDO scheduleTask, GrabDspOrderSnapshotDO snapshot, DspOrderRewardStrategyDO rewardStrategy) {
        logger.info("SelectBroadcastGrabProcess_CreateDriverOrder", "grab type:{}", snapshot.getGrabType().name());
        DriverOrderVO driverOrder = null;
        if (Objects.equals(snapshot.getGrabType(), GrabDspOrderSnapshotTypeEnum.VBK_SUPPLIER)) {
            // vbk人工抢单
            VBKDriverGrabOrderDO vbkDriverGrabOrder = selectVBKGrabOrderProcess.queryVBKDriverGrabOrder(model.getOrder());
            driverOrder = driverOrderFactory.create(model, transportGroup, scheduleTask, vbkDriverGrabOrder, rewardStrategy);
        } else if (Objects.equals(snapshot.getGrabType(), GrabDspOrderSnapshotTypeEnum.VBK_DISPATCHER)) {
            // VBK调度抢单，使用订单上的供应商id
            driverOrder = driverOrderFactory.createBySupplier(model, new SupplierVO(model.getOrder().getSupplierId().longValue()), transportGroup, scheduleTask, snapshot, rewardStrategy);
        } else {
            // 系统抢单
            driverOrder = driverOrderFactory.create(model, scheduleTask);
        }
        if (driverOrder == null) {
            logger.warn("create_driver_order_error", "grab type:{}", snapshot.getGrabType().name());
        }
        return driverOrder;
    }

    /**
     * 应单
     * @return
     */
    public String confirmOrder(DspOrderVO dspOrder, DriverVO driver, VehicleVO vehicle, TransportGroupVO transportGroup, DriverOrderVO driverOrder, ScheduleTaskDO task, GrabDspOrderSnapshotDO snapshot) {
        try {
            OrderStatusEvent evnet = snapshot.getGrabType().isVBK() ? OrderStatusEvent.VBK_DRIVER_GRAB : OrderStatusEvent.SYSTEM_ASSIGN;
            SupplierVO supplier = snapshot.getGrabType().isVBK() ? new SupplierVO(dspOrder.getSupplierId().longValue()) : new SupplierVO(driver.getSupplier().getSupplierId());
            DriverAndCarConfirmVO confirmVO = DriverAndCarConfirmVO.builder()
                    .dspOrder(dspOrder)
                    .serviceProvider(new ServiceProviderVO(dspOrder.getSpId()))
                    .supplier(supplier)
                    .transportGroup(transportGroup)
                    .driver(driver)
                    .vehicle(vehicle)
                    .duid(DuidVO.of(task))
                    .driverOrderId(driverOrder == null ? StringUtils.EMPTY : driverOrder.getDriverOrderId())
                    .rewardAmount(task.getReward().toString())
                    .vbkRewardAmount(task.getVbkReward().toString())
                    .event(evnet)
                    .operator(OperatorVO.systemOperator())
                    .build();
            // 应单
            selectVBKGrabOrderProcess.confirm(dspOrder, confirmVO);
            return GrabOrderCode.SUCCESS.getCode();
        } catch (Exception e) {
            logger.warn(e);
            sendDriverOrderConfirmFailMessage(driverOrder);
        }
        return GrabOrderCode.ORDER_TAKEN_FAIL.getCode();
    }

    /**
     * 抢单结果通知
     * @param results
     */
    public void notice(DspOrderVO dspOrder, List<GrabOrderResultVO> results) {
        try {
            // 抢单结果映射
            Map<String /*code*/, String /*desc*/> mapping = broadcastGrabConfig.getMap(ConfigKey.BROADCAST_GRAB_RESULT_KEY);
            boolean driverQmqAddUcsSwitch = sysSwitchConfigGateway.getDriverQmqAddUcsSwitch();
            Map<Long, DriverUdlVO> drvUdlMap = null;
            if (driverQmqAddUcsSwitch) {
                Set<Long> driverIds = results.stream().filter(t -> !GrabOrderCode.isSuccess(t.getResultCode())).map(GrabOrderResultVO::getIndex).map(GrabDspOrderDriverIndexDO::getDriverId).filter(Objects::nonNull).distinct().collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(driverIds)) {
                    drvUdlMap = queryDriverService.getDrvUdlMap(driverIds);
                }
            }
            for (GrabOrderResultVO result : results) {
                GrabDspOrderDriverIndexDO index = result.getIndex();
                Long driverId = index.getDriverId();
                String code = result.getResultCode();
                if (GrabOrderCode.isSuccess(code)) {
                    // 司机端不消费抢单成功消息了
                    // 抢单成功消息
//                    messageProducer.send(new GrabOrderSuccessEvent(driverId, grabOrder.getDuid(), grabOrder.getDriverOrderId(), grabOrder.getUserOrderId(), code.getCode(), mapping.getOrDefault(code.getCode(), StringUtils.EMPTY)));
                    continue;
                }
                if (ParentCategoryEnum.DAY.getCode().equals(dspOrder.getCategoryCode().getParentType())) {
                    // 抢单失败消息
                    messageProducer.send(new GuideGrabOrderFailEvent(driverId, index.getSubmitDuid(), index.getDspOrderId(), dspOrder.getUserOrderId(), result.getResultCode()));
                } else {
                    // 抢单失败消息
                    if (!driverQmqAddUcsSwitch) {
                        messageProducer.send(new GrabOrderFailEvent(driverId, index.getSubmitDuid(), index.getDspOrderId(), dspOrder.getUserOrderId(), result.getResultCode(), mapping.getOrDefault(result.getResultCode(), StringUtils.EMPTY)));
                        continue;
                    }
                    DriverUdlVO driverUdlVO = drvUdlMap.get(driverId);
                    CatUtil.doWithUdlOverride(() -> {
                        messageProducer.send(new GrabOrderFailEvent(driverId, index.getSubmitDuid(), index.getDspOrderId(), dspOrder.getUserOrderId(), result.getResultCode(), mapping.getOrDefault(result.getResultCode(), StringUtils.EMPTY)));
                        return Boolean.TRUE;
                    }, driverUdlVO.getUdl(), driverUdlVO.getRequestFrom());
                }
            }
        } catch (Exception e) {
            logger.error(e);
        }
    }

    public String getGrabOrderCode(CheckCode checkCode){
        return broadcastGrabFailConfig.getString(String.valueOf(checkCode.getCode()), GrabOrderCode.ORDER_TAKEN_FAIL.getCode());
    }
}
