package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.service.QueryDriverLocationService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.DriverWorkTimeUtil;
import com.ctrip.dcs.domain.common.value.DriverLocationVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.common.value.XproductVO;
import com.ctrip.dcs.domain.dsporder.entity.QueryDistanceReqDTO;
import com.ctrip.dcs.domain.dsporder.entity.QueryDistanceResDTO;
import com.ctrip.dcs.domain.dsporder.gateway.PlatformGeoServiceGateway;
import com.ctrip.dcs.domain.dsporder.value.XSkuCategoryCode;
import com.ctrip.dcs.domain.helper.Cats;
import com.ctrip.dcs.domain.schedule.dto.BufferConfig;
import com.ctrip.dcs.domain.schedule.gateway.ConflictGateway;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.igt.framework.common.base.GsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.spring.InstanceLocator;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

public class DriverEmptyDrivingDurationVisitor extends AbstractVisitor {

    protected final Logger log = LoggerFactory.getLogger(DriverEmptyDrivingDurationVisitor.class);
    protected final String TITLE = "DriverEmptyDrivingDurationVisitor";
    protected final String NEAR_USE_TIME_THRESHOLD_HOURS = "f22.near.use.time.threshold.hours";
    protected final String NEAR_WORK_TIME_THRESHOLD_HOURS = "f22.near.work.time.threshold.hours";

    // 位置服务
    protected final QueryDriverLocationService driverLocationService;
    // 测距服务
    protected final PlatformGeoServiceGateway geoServiceGateway;
    // 订单服务
    protected final QueryDspOrderService dispatchOrderService;
    // 冲突服务
    protected final ConflictGateway conflictGateway;

    // 待派订单
    protected final DspOrderVO dispatchOrder;
    // 司机列表
    protected final List<DriverVO> driverList;

    // 运行时构造函数
    public DriverEmptyDrivingDurationVisitor(DspOrderVO dispatchOrder, List<DriverVO> driverList) {
        this(dispatchOrder, driverList, InstanceLocator.getInstance(ConflictGateway.class), InstanceLocator.getInstance(QueryDspOrderService.class), InstanceLocator.getInstance(PlatformGeoServiceGateway.class), InstanceLocator.getInstance(QueryDriverLocationService.class));
    }

    // 测试用构造函数
    public DriverEmptyDrivingDurationVisitor(DspOrderVO dispatchOrder, List<DriverVO> driverList, ConflictGateway conflictGateway, QueryDspOrderService dispatchOrderService, PlatformGeoServiceGateway geoServiceGateway, QueryDriverLocationService driverLocationService) {
        this.dispatchOrder = dispatchOrder;
        this.driverList = driverList;
        this.conflictGateway = conflictGateway;
        this.dispatchOrderService = dispatchOrderService;
        this.geoServiceGateway = geoServiceGateway;
        this.driverLocationService = driverLocationService;
    }

    // 司机过滤列表
    protected final Map<DriverVO, String> filterList = Maps.newHashMap();

    // 司机前向单
    protected Map<DriverVO, DspOrderVO> driverForwardOrderMap = Maps.newHashMap();
    // 司机后向单
    protected Map<DriverVO, DspOrderVO> driverBackwardOrderMap = Maps.newHashMap();
    // 司机实时位置
    protected Map<DriverVO, DriverLocationVO.LocationVO> driverLocationMap = Maps.newHashMap();
    // 相邻单场景类型
    protected Map<DriverVO, RelatedOrderScene> relatedOrderSceneMap = Maps.newHashMap();
    // 预估路径请求
    protected Table<DriverVO, RouteType, EstimatePath.Request> estimatePathRequestTable = HashBasedTable.create();
    // 预估路径结果
    protected Map<EstimatePath.Request, EstimatePath.Response> estimatePathResponseMap = Maps.newHashMap();
    // 缓冲配置请求
    protected Table<DriverVO, RouteType, BufferConfig.Request> bufferConfigRequestTable = HashBasedTable.create();
    // 缓冲配置结果
    protected Map<BufferConfig.Request, BufferConfig.Response> bufferConfigResponseMap = Maps.newHashMap();

    @Override
    public void visit(SortContext context) {
        // 1.查询司机实时位置
        Cats.runOrThrow(TITLE, "LoadDriverLocationData", () -> loadDriverLocationData(context));
        // 2.查询相邻单数据
        Cats.runOrThrow(TITLE, "LoadRelatedOrderData", () -> loadRelatedOrderData(context));
        // 3.判断相邻单场景
        Cats.runOrThrow(TITLE, "ProcessRelatedOrderScene", () -> processRelatedOrderScene(context));
        // 4.准备测距请求参数
        Cats.runOrThrow(TITLE, "PrepareEstimatePathRequest", () -> prepareEstimatePathRequest(context));
        // 5.批量请求测距服务
        Cats.runOrThrow(TITLE, "ProcessEstimatePathRequest", () -> processEstimatePathRequest(context));
        // 6.获取所有场景的Buffer配置参数
        Cats.runOrThrow(TITLE, "PrepareBufferConfigRequest", () -> prepareBufferConfigRequest(context));
        // 7.批量请求Buffer配置服务
        Cats.runOrThrow(TITLE, "ProcessBufferConfigRequest", () -> processBufferConfigRequest(context));
        // 8.计算最终的空驶结果并写入上下文
        Cats.runOrThrow(TITLE, "CalculateDriverEmptyDuration", () -> calculateDriverEmptyDuration(context));
    }

    /****************************************流程步骤****************************************/

    // 1.查询司机实时位置
    protected void loadDriverLocationData(SortContext context) {
        // 司机映射
        Map<Long, DriverVO> map = getDriverList().stream().collect(Collectors.toMap(DriverVO::getDriverId, Function.identity()));
        // 查询当前位置
        List<DriverLocationVO> response = driverLocationService.queryDriverLocation(map.keySet());
        // 写入上下文
        for (DriverLocationVO item : Optional.ofNullable(response).orElse(Collections.emptyList())) {
            DriverVO driver = map.get(item.getDriverId());
            DriverLocationVO.LocationVO location = Optional.ofNullable(item.getLocations()).orElse(Collections.emptyList()).stream().findFirst().orElse(null);
            if (Objects.isNull(location)) {
                filter(driver, "DriverLocationLoadFail", null);
            } else {
                driverLocationMap.put(driver, location);
            }
        }
        log(context, "LoadDriverLocationData", driverLocationMap);
    }

    // 2.查询相邻单数据
    protected void loadRelatedOrderData(SortContext context) {
        // 查询前后向单
        new DriverForwardAndBackwardOrderVisitor(dispatchOrder, getDriverList(), dispatchOrderService).visit(context);
        // 遍历所有司机
        for (DriverVO driver : getDriverList()) {
            // 前向单
            driverForwardOrderMap.put(driver, context.getDspContext().getDriverForwardOrderMap().get(driver.getDriverId()));
            // 后向单
            driverBackwardOrderMap.put(driver, context.getDspContext().getDriverBackwardOrderMap().get(driver.getDriverId()));
        }
        log(context, "LoadRelatedOrderData.ForwardOrder", driverForwardOrderMap);
        log(context, "LoadRelatedOrderData.BackwardOrder", driverBackwardOrderMap);
    }

    // 3.判断相邻单场景
    protected void processRelatedOrderScene(SortContext context) {
        for (DriverVO driver : getDriverList()) {
            DspOrderVO frowardOrder = getForwardOrder(driver);
            DspOrderVO backwardOrder = getBackwardOrder(driver);
            RelatedOrderScene scene = getRelatedOrderScene(context, driver, dispatchOrder, frowardOrder, backwardOrder);
            relatedOrderSceneMap.put(driver, scene);
            Cat.logEvent(TITLE, "Scene." + scene.name());
        }
        log(context, "ProcessRelatedOrderScene", relatedOrderSceneMap);
    }

    // 4.准备测距请求参数
    protected void prepareEstimatePathRequest(SortContext context) {
        for (DriverVO driver : getDriverList()) {
            for (RouteType type : getRouteTypes(driver)) {
                try {
                    estimatePathRequestTable.put(driver, type, generateEstimatePathRequest(driver, type));
                } catch (Throwable e) {
                    filter(driver, "PrepareEstimatePathRequestFail", e.getMessage());
                }
            }
        }
        log(context, "PrepareEstimatePathRequest", estimatePathRequestTable);
    }

    // 5.批量请求测距服务
    protected void processEstimatePathRequest(SortContext context) {
        // 获取所有请求（如果请求不合法则需要过滤掉否则会影响整个批量请求）
        List<EstimatePath.Request> requests = estimatePathRequestTable.values().stream().filter(EstimatePath::valid).toList();
        // 请求外部服务（转换成外部对象）
        List<QueryDistanceResDTO> responses = geoServiceGateway.batchQueryPreBackDistance(requests.stream().map(this::convert).toList());
        // 构建映射结果（这里需要处理重复的请求）
        Map<String, EstimatePath.Response> map = Optional.ofNullable(responses).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(this::key, this:: convert, (v1, v2) -> v2));
        // 获取映射结果（反响从请求查找响应）
        for (EstimatePath.Request request : requests) {
            String key = key(request);
            EstimatePath.Response response = map.get(key);
            estimatePathResponseMap.put(request, response);
        }
        log(context, "ProcessEstimatePathRequest", estimatePathResponseMap);
    }

    // 6.获取所有场景的Buffer配置参数
    protected void prepareBufferConfigRequest(SortContext context) {
        for (DriverVO driver : getDriverList()) {
            for (RouteType type : getRouteTypes(driver)) {
                try {
                    bufferConfigRequestTable.put(driver, type, generateBufferConfigRequest(driver, type));
                } catch (Throwable e) {
                    filter(driver, "PrepareBufferConfigRequestFail", e.getMessage());
                }
            }
        }
        log(context, "PrepareBufferConfigRequest", bufferConfigRequestTable);
    }

    // 7.批量请求Buffer配置服务
    protected void processBufferConfigRequest(SortContext context) {
        // 获取所有请求
        List<BufferConfig.Request> requests = bufferConfigRequestTable.values().stream().toList();
        // 请求外部服务
        Map<BufferConfig.Request, BufferConfig.Response> responses = conflictGateway.queryBufferConfig(requests);
        // 写入结果
        Optional.ofNullable(responses).ifPresent(bufferConfigResponseMap::putAll);
        // 记录日志
        log(context, "ProcessBufferConfigRequest", bufferConfigResponseMap);
    }

    // 8.计算最终的空驶结果并写入上下文
    protected void calculateDriverEmptyDuration(SortContext context) {
        for (DriverVO driver : getDriverList()) {
            context.getDriverEmptyDurationMap().put(driver.getDriverId(), calculateDriverEmptyDuration(driver));
        }
        log(context, "CalculateDriverEmptyDuration", context.getDriverEmptyDurationMap());
    }

    /****************************************核心逻辑****************************************/

    /**
     * 判断相邻单场景
     *
     * @param driver        司机
     * @param dispatchOrder 待派单
     * @param forwardOrder  前向单
     * @param backwardOrder 后向单
     * @return 相邻单场景枚举
     */
    protected RelatedOrderScene getRelatedOrderScene(SortContext context, DriverVO driver, DspOrderVO dispatchOrder, DspOrderVO forwardOrder, DspOrderVO backwardOrder) {
        try {
            // 无前向单、无后向单
            if (Objects.isNull(forwardOrder) && Objects.isNull(backwardOrder)) {
                // 当前派单时间（北京）
                LocalDateTime dispatchOrderTime = LocalDateTime.now();
                // 待派订单用车时间（北京）
                LocalDateTime estimatedUseBeijingTime = LocalDateTimeUtil.of(dispatchOrder.getEstimatedUseTimeBj());
                // 派单时间距离用车时间 > 4小时
                if (LocalDateTimeUtil.between(dispatchOrderTime, estimatedUseBeijingTime, ChronoUnit.MINUTES) > context.getCityProperties(NEAR_USE_TIME_THRESHOLD_HOURS, dispatchOrder.getCityId(), 4) * 60) {
                    return RelatedOrderScene.NoneRelatedOrderA;
                } else {
                    return RelatedOrderScene.NoneRelatedOrderB;
                }
            }
            // 无前向单、有后向单
            if (Objects.isNull(forwardOrder) && Objects.nonNull(backwardOrder)) {
                // 待派订单用车时间（当地）
                LocalDateTime estimatedUseLocalTime = LocalDateTimeUtil.of(dispatchOrder.getEstimatedUseTime());
                // 司机工作开始时间（当地）
                LocalDateTime driverStartWorkLocalTime = DriverWorkTimeUtil.getDriverWorkStartTime(driver, dispatchOrder.getEstimatedUseTime());
                // 数据缺失
                if (Objects.isNull(driverStartWorkLocalTime)) {
                    return RelatedOrderScene.MissData;
                }
                // 待派订单预估用车时间 ≥ 司机工作开始时间 + 2小时
                if (LocalDateTimeUtil.between(driverStartWorkLocalTime, estimatedUseLocalTime, ChronoUnit.MINUTES) >= context.getCityProperties(NEAR_WORK_TIME_THRESHOLD_HOURS, dispatchOrder.getCityId(), 2) * 60) {
                    return RelatedOrderScene.OnlyBackwardOrderA;
                } else {
                    return RelatedOrderScene.OnlyBackwardOrderB;
                }
            }
            // 有前向单、无后向单
            if (Objects.nonNull(forwardOrder) && Objects.isNull(backwardOrder)) {
                return RelatedOrderScene.OnlyForwardOrder;
            }
            // 有前向单、有后向单
            if (Objects.nonNull(forwardOrder) && Objects.nonNull(backwardOrder)) {
                return RelatedOrderScene.BothForwardAndBackwardOrder;
            }
        } catch (Throwable e) {
            log.error("GetRelatedOrderScene", e);
        }
        // 异常兜底
        return RelatedOrderScene.Unknown;
    }


    /**
     * 生成预估路径请求体
     *
     * @param driver 司机
     * @param type   路线类型
     * @return
     */
    protected EstimatePath.Request generateEstimatePathRequest(DriverVO driver, RouteType type) {
        switch (type) {
            case DriverHomeLocationToDispatchOrderStartPoint -> {
                return EstimatePath.Request.builder()
                        .startLatitude(BigDecimal.valueOf(driver.getAddressLatitude()))
                        .startLongitude(BigDecimal.valueOf(driver.getAddressLongitude()))
                        .endLatitude(dispatchOrder.getActualFromLatitude())
                        .endLongitude(dispatchOrder.getActualFromLongitude())
                        .build();
            }
            case DriverCurrentLocationToDispatchOrderStartPoint -> {
                return EstimatePath.Request.builder()
                        .startLatitude(getDriverLatitude(driver))
                        .startLongitude(getDriverLongitude(driver))
                        .endLatitude(dispatchOrder.getActualFromLatitude())
                        .endLongitude(dispatchOrder.getActualFromLongitude())
                        .build();
            }
            case DispatchOrderEndPointToBackwardOrderStartPoint -> {
                return EstimatePath.Request.builder()
                        .startLatitude(dispatchOrder.getActualToLatitude())
                        .startLongitude(dispatchOrder.getActualToLongitude())
                        .endLatitude(getBackwardOrder(driver).getActualFromLatitude())
                        .endLongitude(getBackwardOrder(driver).getActualFromLongitude())
                        .build();
            }
            case ForwardOrderOrderEndPointToDispatchOrderStartPoint -> {
                return EstimatePath.Request.builder()
                        .startLatitude(getForwardOrder(driver).getActualToLatitude())
                        .startLongitude(getForwardOrder(driver).getActualToLongitude())
                        .endLatitude(dispatchOrder.getActualFromLatitude())
                        .endLongitude(dispatchOrder.getActualFromLongitude())
                        .build();
            }
        }
        return null;
    }


    /**
     * 计算司机的空驶时长
     *
     * @param driver 司机
     * @return LBS + Buffer
     */
    protected Double calculateDriverEmptyDuration(DriverVO driver) {
        switch (getRelatedOrderScene(driver)) {
            case OnlyForwardOrder:
            case NoneRelatedOrderA:
            case NoneRelatedOrderB:
            case OnlyBackwardOrderA:
                return getEstimatePathDurationSeconds(driver) + getBufferConfigDurationSeconds(driver);
            case OnlyBackwardOrderB:
            case BothForwardAndBackwardOrder:
                return (getEstimatePathDurationSeconds(driver) + getBufferConfigDurationSeconds(driver)) / 2;
            default:
                return null;
        }
    }

    /****************************************辅助函数****************************************/


    // 构造BufferConfig请求体
    protected BufferConfig.Request generateBufferConfigRequest(DriverVO driver, RouteType type) {
        return BufferConfig.Request.builder()
                .key(driver.getDriverId() + "." + type.name())
                .productCategoryCode(dispatchOrder.getCategoryCode().getType())
                .cityId(Long.valueOf(dispatchOrder.getCityId()))
                .crossCity(BooleanUtils.negate(Objects.equals(dispatchOrder.getFromCityId(), dispatchOrder.getToCityId())))
                .meetAndGreet(hasMeatAndGreet(dispatchOrder))
                .airportCode((CategoryCodeEnum.FROM_AIRPORT.equals(dispatchOrder.getCategoryCode()) || CategoryCodeEnum.TO_AIRPORT.equals(dispatchOrder.getCategoryCode())) ? dispatchOrder.getTargetId() : null)
                .terminalId((CategoryCodeEnum.FROM_AIRPORT.equals(dispatchOrder.getCategoryCode()) || CategoryCodeEnum.TO_AIRPORT.equals(dispatchOrder.getCategoryCode())) ? Optional.ofNullable(dispatchOrder.getTerminalId()).filter(StringUtils::isNotBlank).map(Long::valueOf).orElse(null) : null)
                .stationId((CategoryCodeEnum.FROM_STATION.equals(dispatchOrder.getCategoryCode()) || CategoryCodeEnum.TO_STATION.equals(dispatchOrder.getCategoryCode())) ? Optional.ofNullable(dispatchOrder.getTargetId()).filter(StringUtils::isNotBlank).map(Long::valueOf).orElse(null) : null)
                .estimatedUseLocalTime(LocalDateTimeUtil.of(RouteType.DispatchOrderEndPointToBackwardOrderStartPoint.equals(type) ? dispatchOrder.getPredicServiceStopTime() : dispatchOrder.getEstimatedUseTime()))
                .estimatedUseBeijingTime(LocalDateTimeUtil.of(RouteType.DispatchOrderEndPointToBackwardOrderStartPoint.equals(type) ? dispatchOrder.getPredicServiceStopTime() : dispatchOrder.getEstimatedUseTime()))
                .estimatedDrivingDurationSeconds(getEstimatePathDurationSeconds(driver, type).longValue())
                .build();
    }

    /****************************************辅助枚举****************************************/

    protected enum RelatedOrderScene {
        /*无前后向单且未临近用车时间*/
        NoneRelatedOrderA,
        /*无前后向单且已临近用车时间*/
        NoneRelatedOrderB,
        /*仅有后向单且未临近开始工作时间*/
        OnlyBackwardOrderA,
        /*仅有后向单且已临近开始工作时间*/
        OnlyBackwardOrderB,
        /*仅有前向单*/
        OnlyForwardOrder,
        /*同时有前后向单*/
        BothForwardAndBackwardOrder,
        /*缺失数据*/
        MissData,
        /*未知兜底*/
        Unknown;
    }

    public enum RouteType {
        // 【司机家庭住址】～【待派订单上车点】
        DriverHomeLocationToDispatchOrderStartPoint,
        // 【司机实时位置】～【待派订单上车点】
        DriverCurrentLocationToDispatchOrderStartPoint,
        // 【待派订单下车点】～【后向订单上车点】
        DispatchOrderEndPointToBackwardOrderStartPoint,
        // 【前向订单下车点】～【待派订单上车点】
        ForwardOrderOrderEndPointToDispatchOrderStartPoint;
    }

    protected List<RouteType> getRouteTypes(RelatedOrderScene scene) {
        switch (scene) {
            case NoneRelatedOrderA:
                return List.of(RouteType.DriverHomeLocationToDispatchOrderStartPoint);
            case NoneRelatedOrderB:
                return List.of(RouteType.DriverCurrentLocationToDispatchOrderStartPoint);
            case OnlyBackwardOrderA:
                return List.of(RouteType.DispatchOrderEndPointToBackwardOrderStartPoint);
            case OnlyBackwardOrderB:
                return List.of(RouteType.DriverHomeLocationToDispatchOrderStartPoint, RouteType.DispatchOrderEndPointToBackwardOrderStartPoint);
            case OnlyForwardOrder:
                return List.of(RouteType.ForwardOrderOrderEndPointToDispatchOrderStartPoint);
            case BothForwardAndBackwardOrder:
                return List.of(RouteType.ForwardOrderOrderEndPointToDispatchOrderStartPoint, RouteType.DispatchOrderEndPointToBackwardOrderStartPoint);
            default:
                return Collections.emptyList();
        }
    }

    /****************************************读写函数****************************************/

    protected List<RouteType> getRouteTypes(DriverVO driver) {
        return Optional.ofNullable(driver).map(this::getRelatedOrderScene).map(this::getRouteTypes).orElse(Collections.emptyList());
    }

    protected List<DriverVO> getDriverList() {
        return Optional.ofNullable(driverList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(e -> filterList.containsKey(e) == false)
                .toList();
    }

    // 获取该司机的所有预估路径时长
    protected Double getEstimatePathDurationSeconds(DriverVO driver) {
        return Optional.ofNullable(driver)
                .map(estimatePathRequestTable::row)
                .orElse(Collections.emptyMap())
                .values()
                .stream()
                .map(estimatePathResponseMap::get)
                .filter(Objects::nonNull)
                .map(EstimatePath.Response::getDuration)
                .filter(Objects::nonNull)
                .map(Double::valueOf)
                .reduce(Double::sum)
                .orElse(0D);
    }

    protected Double getEstimatePathDurationSeconds(DriverVO driver, RouteType type) {
        return Optional.ofNullable(getEstimatePathResponse(driver, type)).map(this::getEstimatePathDurationSeconds).orElse(null);
    }

    protected EstimatePath.Response getEstimatePathResponse(DriverVO driver, RouteType type) {
        return Optional.ofNullable(estimatePathRequestTable.get(driver, type)).map(estimatePathResponseMap::get).orElse(null);
    }


    // 获取该司机的所有Buffer时长
    protected Double getBufferConfigDurationSeconds(DriverVO driver) {
        return Optional.ofNullable(driver)
                .map(bufferConfigRequestTable::row)
                .orElse(Collections.emptyMap())
                .values()
                .stream()
                .map(bufferConfigResponseMap::get)
                .filter(Objects::nonNull)
                .map(this::getBufferConfigDurationSeconds)
                .reduce(Double::sum)
                .orElse(0D);
    }

    protected RelatedOrderScene getRelatedOrderScene(DriverVO driver) {
        return relatedOrderSceneMap.get(driver);
    }

    protected DspOrderVO getBackwardOrder(DriverVO driver) {
        return driverBackwardOrderMap.get(driver);
    }

    protected DspOrderVO getForwardOrder(DriverVO driver) {
        return driverForwardOrderMap.get(driver);
    }

    protected BigDecimal getDriverLatitude(DriverVO driver) {
        return Optional.ofNullable(driver)
                .map(driverLocationMap::get)
                .map(DriverLocationVO.LocationVO::getLatitude)
                .orElse(null);
    }

    protected BigDecimal getDriverLongitude(DriverVO driver) {
        return Optional.ofNullable(driver)
                .map(driverLocationMap::get)
                .map(DriverLocationVO.LocationVO::getLongitude)
                .orElse(null);
    }

    /****************************************辅助函数****************************************/

    protected Double getEstimatePathDurationSeconds(EstimatePath.Response in) {
        return Optional.ofNullable(in).map(EstimatePath.Response::getDuration).map(Long::doubleValue).orElse(null);
    }

    protected Double getBufferConfigDurationSeconds(BufferConfig.Response in) {
        return Optional.ofNullable(in.getAirportBufferSeconds()).orElse(0D) + Optional.ofNullable(in.getStationBufferSeconds()).orElse(0D) + Optional.ofNullable(in.getTrafficBufferSeconds()).orElse(0D);
    }

    // 判断是否有举牌接机
    protected boolean hasMeatAndGreet(DspOrderVO dispatchOrder) {
      return Optional.ofNullable(dispatchOrder)
                .map(DspOrderVO::getXproductList)
                .orElse(Collections.emptyList())
                .stream()
                .map(XproductVO::getCategoryCode)
                .collect(Collectors.toSet())
                .contains(XSkuCategoryCode.PICK_UP_CARD.getCode());
    }


    /****************************************辅助函数****************************************/

    protected EstimatePath.Response convert(QueryDistanceResDTO in) {
        return EstimatePath.Response.builder()
                .distance(Optional.ofNullable(in.getDistance()).map(Long::valueOf).orElse(null))
                .duration(Optional.ofNullable(in.getDuration()).map(Long::valueOf).orElse(null))
                .build();
    }

    protected QueryDistanceReqDTO convert(EstimatePath.Request in) {
        QueryDistanceReqDTO out = new QueryDistanceReqDTO();
        out.setOriginLongitude(in.getStartLongitude());
        out.setOriginLatitude(in.getStartLatitude());
        out.setDestinationLatitude(in.getEndLatitude());
        out.setDestinationLongitude(in.getEndLongitude());
        return out;
    }

    protected String key(EstimatePath.Request in) {
        return key((in.getStartLatitude()), in.getStartLongitude(), in.getEndLatitude(), in.getEndLongitude());
    }

    protected String key(QueryDistanceResDTO in) {
        return key(in.getOriginLatitude(), in.getOriginLongitude(), in.getDestinationLatitude(), in.getDestinationLongitude());
    }

    // make sure use the same scale
    public String key(BigDecimal fromLatitude, BigDecimal fromLongitude, BigDecimal toLatitude, BigDecimal toLongitude) {
        return coordinate(fromLatitude) + "," + coordinate(fromLongitude) + "~" + coordinate(toLatitude) + "," + coordinate(toLongitude);
    }

    // 统一保留6位小数
    protected String coordinate(BigDecimal in) {
        return Optional.ofNullable(in).map(e -> e.setScale(6, RoundingMode.HALF_UP)).map(String::valueOf).orElse(null);
    }

    /****************************************值对象类****************************************/

    protected static class EstimatePath {
        @Getter
        @Builder
        public static class Request {
            BigDecimal startLatitude;
            BigDecimal startLongitude;
            BigDecimal endLatitude;
            BigDecimal endLongitude;
        }

        public static boolean valid(Request in) {
            return Objects.nonNull(in.getStartLatitude()) && Objects.nonNull(in.getStartLongitude()) && Objects.nonNull(in.getEndLatitude()) && Objects.nonNull(in.getEndLongitude());
        }

        @Getter
        @Builder
        public static class Response {
            Long distance;
            Long duration;
        }
    }

    /****************************************日志工具****************************************/

    protected void filter(DriverVO driver, String reason, String message) {
        Transaction transaction = Cat.newTransaction("App." + TITLE, "Filter." + reason);
        try {
            filterList.put(driver, message);
            transaction.addData("Driver", driver.getDriverId());
            transaction.addData("Message", message);
        } catch (Throwable e) {
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
    }

    protected void log(SortContext context, String title, Object message) {
        try {
            DuidVO duid = context.getDuid();
            Map<String, String> attrs = new HashMap<>();
            attrs.put("duid", JsonUtils.toJson(duid));
            log.info(title, JsonUtils.toJson(message), attrs);
        } catch (Exception e) {
            Cat.logError(e);
        }
    }

}