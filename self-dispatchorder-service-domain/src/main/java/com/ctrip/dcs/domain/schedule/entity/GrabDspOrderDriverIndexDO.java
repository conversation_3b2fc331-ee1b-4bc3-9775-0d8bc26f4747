package com.ctrip.dcs.domain.schedule.entity;

import cn.hutool.core.util.BooleanUtil;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class GrabDspOrderDriverIndexDO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 派发单
     */
    private String dspOrderId;

    /**
     * 司机id
     */
    private Long driverId;

    private Long cityId;

    /**
     * 一级产线
     */
    private ParentCategoryEnum categoryCode;

    /**
     * 是否有效 0-失效；1-有效；2-删除
     */
    private Integer isValid;

    /**
     * 是否开启播报 0-否；1-是
     */
    private Integer isBroadcast;

    /**
     * 是否发起抢单 0-否，1-是
     */
    private Integer isSubmit;

    /**
     * 是否抢单接单 0-否，1-是
     */
    private Integer isTaken;

    /**
     * 预计用车时间（北京）
     */
    private Date estimatedUseTimeBj;

    /**
     * 抢单时间-北京
     */
    private Date submitTimeBj;

    /**
     * 发单时间-北京
     */
    private Date grabPushTimeBj;

    /**
     * 播报时间-北京
     */
    private Date broadcastPushTimeBj;

    private String duid;

    private String submitDuid;

    /**
     * 创建时间
     */
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    private Timestamp datachangeLasttime;

    public GrabDspOrderDriverIndexDO() {
    }

    public GrabDspOrderDriverIndexDO(String dspOrderId, Long driverId, Date estimatedUseTimeBj, String duid, Long cityId, ParentCategoryEnum parentCategoryCode) {
        this.dspOrderId = dspOrderId;
        this.driverId = driverId;
        this.estimatedUseTimeBj = estimatedUseTimeBj;
        this.cityId = cityId;
        this.duid = duid;
        this.isValid = YesOrNo.NO.getCode();
        this.isBroadcast = YesOrNo.NO.getCode();
        this.isSubmit = YesOrNo.NO.getCode();
        this.isTaken = YesOrNo.NO.getCode();
        this.categoryCode = parentCategoryCode;
    }

    public Long getId() {
        return id;
    }

    public String getDspOrderId() {
        return dspOrderId;
    }

    public Long getDriverId() {
        return driverId;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public Integer getIsBroadcast() {
        return isBroadcast;
    }

    public Integer getIsSubmit() {
        return isSubmit;
    }

    public Integer getIsTaken() {
        return isTaken;
    }

    public Date getEstimatedUseTimeBj() {
        return estimatedUseTimeBj;
    }

    public Date getSubmitTimeBj() {
        return submitTimeBj;
    }

    public Date getGrabPushTimeBj() {
        return grabPushTimeBj;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setDspOrderId(String dspOrderId) {
        this.dspOrderId = dspOrderId;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public void setIsBroadcast(Integer isBroadcast) {
        this.isBroadcast = isBroadcast;
    }

    public void setIsSubmit(Integer isSubmit) {
        this.isSubmit = isSubmit;
    }

    public void setIsTaken(Integer isTaken) {
        this.isTaken = isTaken;
    }

    public void setEstimatedUseTimeBj(Date estimatedUseTimeBj) {
        this.estimatedUseTimeBj = estimatedUseTimeBj;
    }

    public void setSubmitTimeBj(Date submitTimeBj) {
        this.submitTimeBj = submitTimeBj;
    }

    public void setGrabPushTimeBj(Date grabPushTimeBj) {
        this.grabPushTimeBj = grabPushTimeBj;
    }

    public Date getBroadcastPushTimeBj() {
        return broadcastPushTimeBj;
    }

    public void setBroadcastPushTimeBj(Date broadcastPushTimeBj) {
        this.broadcastPushTimeBj = broadcastPushTimeBj;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getDuid() {
        return duid;
    }

    public void setDuid(String duid) {
        this.duid = duid;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public ParentCategoryEnum getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(ParentCategoryEnum categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getSubmitDuid() {
        return submitDuid;
    }

    public void setSubmitDuid(String submitDuid) {
        this.submitDuid = submitDuid;
    }

    public GrabDspOrderDriverIndexDO updateGrabPushTime(Date grabPushTimeBj) {
        this.grabPushTimeBj =  grabPushTimeBj;
        this.broadcastPushTimeBj =  grabPushTimeBj;
        return this;
    }

    public GrabDspOrderDriverIndexDO updateValid(Integer valid) {
        this.isValid = valid;
        return this;
    }

    public GrabDspOrderDriverIndexDO updateTaken(Integer taken) {
        this.isTaken = taken;
        return this;
    }

    public GrabDspOrderDriverIndexDO updateBroadcast(Boolean isBroadcast) {
        this.isBroadcast = BooleanUtil.isTrue(isBroadcast) ? YesOrNo.YES.getCode() : YesOrNo.NO.getCode();;
        return this;
    }

    public void updateBroadcastPushTime(SubSkuVO subSkuVO) {
        int seconds = Optional.ofNullable(subSkuVO).map(SubSkuVO::getRetrySecond).map(Long::intValue).orElse(15 * 60);
        this.broadcastPushTimeBj = DateUtil.addSeconds(this.broadcastPushTimeBj, seconds);
    }

    public GrabDspOrderDriverIndexDO submit(String duid) {
        this.isSubmit = YesOrNo.YES.getCode();
        this.submitTimeBj = new Date();
        this.submitDuid = duid;
        return this;
    }

    public GrabDspOrderDriverIndexDO updateEstimatedUseTime(Date estimatedUseTimeBj) {
        this.estimatedUseTimeBj = estimatedUseTimeBj;
        return this;
    }

    public GrabDspOrderDriverIndexDO updateDuid(String duid) {
        this.duid = duid;
        return this;
    }
}
