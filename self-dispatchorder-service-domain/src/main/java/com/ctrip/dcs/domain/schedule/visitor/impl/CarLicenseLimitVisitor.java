package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.TrafficControlGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class CarLicenseLimitVisitor extends AbstractVisitor {

    private static final Logger logger = LoggerFactory.getLogger(CarLicenseLimitVisitor.class);


    private DspOrderVO dspOrder;

    private List<DriverVO> drivers;

    private TrafficControlGateway trafficControlGateway;

    public CarLicenseLimitVisitor(DspOrderVO dspOrder, List<DriverVO> drivers) {
        Assert.notNull(dspOrder);
        Assert.notEmpty(drivers);
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.trafficControlGateway = getInstance(TrafficControlGateway.class);
    }

    public CarLicenseLimitVisitor(DspOrderVO dspOrder, List<DriverVO> drivers, TrafficControlGateway trafficControlGateway) {
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.trafficControlGateway = trafficControlGateway;
    }

    @Override
    public void visit(CheckContext context) {
        List<String> carLicenses = context.getLimitLicensePlateNumberList();
        List<DriverVO> driver = this.drivers.stream().filter(d -> !carLicenses.contains(d.getCar().getCarLicense())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(driver)) {
            return;
        }
        Integer fromCityId = dspOrder.getFromCityId();
        Integer toCityId = dspOrder.getToCityId();
        logger.info("CarLicenseLimitVisitor_visit", dspOrder.getDspOrderId());
        if(Objects.isNull(fromCityId) || Objects.isNull(toCityId)){
            MetricsUtil.recordValue("query.driver.traffic.control.param.error", 1);
            List<String> limit = trafficControlGateway.queryDriverTrafficControl(dspOrder.getCityId(), dspOrder, driver);
            logger.info("CarLicenseLimitVisitor_queryDriverTrafficControl1", JsonUtil.toJson(limit));
            carLicenses.addAll(limit);
            return;
        }
        if(fromCityId.equals(toCityId)){
            List<String> limit = trafficControlGateway.queryDriverTrafficControl(dspOrder.getCityId(), dspOrder, driver);
            logger.info("CarLicenseLimitVisitor_queryDriverTrafficControl2", JsonUtil.toJson(limit));
            carLicenses.addAll(limit);
            return;
        }
        List<String> fromLimit = trafficControlGateway.queryDriverTrafficControl(fromCityId, dspOrder, driver);
        List<String> toLimit = trafficControlGateway.queryDriverTrafficControl(toCityId, dspOrder, driver);
        fromLimit.addAll(toLimit);
        Set<String> collect = new HashSet<>(fromLimit);
        carLicenses.addAll(collect);
        logger.info("CarLicenseLimitVisitor_queryDriverTrafficControl3", JsonUtil.toJson(carLicenses));
    }
}
