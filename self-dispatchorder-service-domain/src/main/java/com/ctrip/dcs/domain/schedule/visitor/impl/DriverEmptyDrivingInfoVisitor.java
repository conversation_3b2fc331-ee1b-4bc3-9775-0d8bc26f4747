package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.enums.DspStage;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.DriverDeadHeadDisService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.value.DeadHeadDisModelVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class DriverEmptyDrivingInfoVisitor extends AbstractVisitor {
    private static final Logger logger = LoggerFactory.getLogger(DriverEmptyDrivingInfoVisitor.class);

    private DspOrderVO dspOrder;

    private List<DriverVO> drivers;

    private DriverDeadHeadDisService driverDeadHeadDisService;

    private DriverLocationVisitor driverLocationVisitor;

    private DriverOrderListVisitor driverOrderListVisitor;

    private DriverSimpleOrderListVisitor driverSimpleOrderListVisitor;

    private Integer queryOrderListSwitch;

    public DriverEmptyDrivingInfoVisitor(DspOrderVO dspOrder, List<DriverVO> drivers, DspStage dspStage) {
        Assert.notNull(dspOrder);
        Assert.notEmpty(drivers);
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.driverDeadHeadDisService = getInstance(DriverDeadHeadDisService.class);
        this.driverLocationVisitor = new DriverLocationVisitor(drivers);
        this.driverOrderListVisitor = new DriverOrderListVisitor(dspOrder, drivers, getInstance(QueryDspOrderService.getServiceName(dspStage), QueryDspOrderService.class));
        this.driverSimpleOrderListVisitor = new DriverSimpleOrderListVisitor(dspOrder, drivers, Objects.equals(dspStage, DspStage.DSP));
        this.queryOrderListSwitch = getInstance("commonConfConfig", ConfigService.class).getInteger("queryOrderListSwitch", 0);
    }

    public DriverEmptyDrivingInfoVisitor(DspOrderVO dspOrder, List<DriverVO> drivers, DriverDeadHeadDisService driverDeadHeadDisService, DriverLocationVisitor driverLocationVisitor, DriverOrderListVisitor driverOrderListVisitor) {
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.driverDeadHeadDisService = driverDeadHeadDisService;
        this.driverLocationVisitor = driverLocationVisitor;
        this.driverOrderListVisitor = driverOrderListVisitor;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, DeadHeadDisModelVO> map = context.getDriverEmptyDrivingInfo4OrderMileageMap();
        List<DriverVO> filter = filter(drivers, map.keySet());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        driverLocationVisitor.visit(context);
        if (Objects.equals(1, queryOrderListSwitch)) {
            Cat.logEvent("dcs.dsp.queryOrderList.switch", "1");
            driverSimpleOrderListVisitor.visit(context);
        } else {
            Cat.logEvent("dcs.dsp.queryOrderList.switch", "0");
            driverOrderListVisitor.visit(context);
        }
        //先查到需要的前后单，然后再计算前后空驶
        Map<Long, DriverRelateOrderVO> relateOrderMap = queryPreAndBackOrderInWorkTime(filter, dspOrder, context.getContext().getOrderListMap());
        Map<Long, DeadHeadDisModelVO> result = driverDeadHeadDisService.calculateDeadHeadDisForOrderMileage(dspOrder, drivers, context.getDriverLastLocationMap(), relateOrderMap);
        map.putAll(result);
        logger.info("DriverEmptyDrivingInfoVisitor", JacksonSerializer.INSTANCE().serialize(map));
    }

    /**
     * 查询司机工作时间内的前后向单
     *
     * @param drivers
     * @param dspOrder 待派订单
     * @return
     */
    private Map<Long, DriverRelateOrderVO> queryPreAndBackOrderInWorkTime(List<DriverVO> drivers, DspOrderVO dspOrder, Map<Long,List<DspOrderVO>> allDspOrderMap) {
        Map<Long, DriverRelateOrderVO> res = Maps.newHashMap();
        // 查询前向单和后向单
        for (DriverVO driver : drivers) {
            if (!allDspOrderMap.containsKey(driver.getDriverId())) {
                continue;
            }
            DspOrderVO prevOrder =null;
            DspOrderVO nextOrder =null;
            List<DspOrderVO> takenOrderList = allDspOrderMap.get(driver.getDriverId());
            for (DspOrderVO takenOrderInfo : takenOrderList) {
                if (takenOrderInfo.getEstimatedUseTime().before(dspOrder.getEstimatedUseTime())) {
                    if (prevOrder == null || takenOrderInfo.getEstimatedUseTime().after(prevOrder.getEstimatedUseTime())) {
                        prevOrder = takenOrderInfo;
                    }
                }
                if (takenOrderInfo.getEstimatedUseTime().after(dspOrder.getEstimatedUseTime())) {
                    if (nextOrder == null || takenOrderInfo.getEstimatedUseTime().before(nextOrder.getEstimatedUseTime())) {
                        nextOrder = takenOrderInfo;
                    }
                }
            }
            DriverRelateOrderVO relateOrderVO = new DriverRelateOrderVO();
            relateOrderVO.setDriverId(driver.getDriverId());
            relateOrderVO.setCurDspOrderId(dspOrder.getDspOrderId());
            if (prevOrder != null) {
                relateOrderVO.setFrowardOrderInfo(prevOrder);
            }
            if (nextOrder != null) {
                relateOrderVO.setBackwardOrderInfo(nextOrder);
            }
            res.put(driver.getDriverId(),relateOrderVO);
        }
        return res;
    }
}
