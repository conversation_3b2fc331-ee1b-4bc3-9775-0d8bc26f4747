package com.ctrip.dcs.domain.schedule.value;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class DrvInventoryCheckResultVO {
    /**
     * 司机id
     */
    Long driverId;
    /**
     * 检查结果code，检查结果code码
     * @see  com.ctrip.dcs.domain.schedule.check.CheckResMapping.DrivInventoryStatus
     */
    String checkResultCode;
    /**
     * 检查结果描述，
     */
    String checkResultDesc;
//    /**
//     * 派发订单号
//     */
//    String dspOrderId;
}
