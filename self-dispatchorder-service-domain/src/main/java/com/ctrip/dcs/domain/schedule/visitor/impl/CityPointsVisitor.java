package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.schedule.gateway.DriverPointsGateway;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.value.CityPointVO;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class CityPointsVisitor extends AbstractVisitor {

    private Integer cityId;

    private DriverPointsGateway dcsDriverLevelServiceGateway;

    public CityPointsVisitor(Integer cityId) {
        Assert.notNull(cityId);
        this.cityId = cityId;
        this.dcsDriverLevelServiceGateway = getInstance(DriverPointsGateway.class);
    }

    public CityPointsVisitor(Integer cityId, DriverPointsGateway dcsDriverLevelServiceGateway) {
        this.cityId = cityId;
        this.dcsDriverLevelServiceGateway = dcsDriverLevelServiceGateway;
    }

    @Override
    public void visit(SortContext context) {
        Map<Integer, CityPointVO> map = context.getCityPointInfoMap();
        if (map.containsKey(cityId)) {
            return;
        }
        CityPointVO cityPoint = dcsDriverLevelServiceGateway.queryCityPointsInfo(cityId);
        map.put(cityId, cityPoint);
    }
}
