package com.ctrip.dcs.domain.schedule.visitor.impl;

import com.ctrip.dcs.domain.common.service.QueryDriverTiredService;
import com.ctrip.dcs.domain.common.value.DriverTiredLimitVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DriverTiredLimitVisitor extends AbstractVisitor {

    private DspOrderVO dspOrder;

    private List<DriverVO> drivers;

    private QueryDriverTiredService queryDriverTiredService;

    public DriverTiredLimitVisitor(DspOrderVO dspOrder, List<DriverVO> drivers) {
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.queryDriverTiredService = getInstance(QueryDriverTiredService.class);
    }

    public DriverTiredLimitVisitor(DspOrderVO dspOrder, List<DriverVO> drivers, QueryDriverTiredService queryDriverTiredService) {
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.queryDriverTiredService = queryDriverTiredService;
    }

    @Override
    public void visit(CheckContext context) {
        List<DriverTiredLimitVO> tired = queryDriverTiredService.query(dspOrder, drivers);
        Set<Long> driverIds = Optional.ofNullable(tired)
                .orElse(Collections.emptyList())
                .stream()
                .filter(DriverTiredLimitVO::isTired)
                .map(DriverTiredLimitVO::getDriverId)
                .collect(Collectors.toSet());
        context.getTiredLimitDriverIds().addAll(driverIds);
    }
}
