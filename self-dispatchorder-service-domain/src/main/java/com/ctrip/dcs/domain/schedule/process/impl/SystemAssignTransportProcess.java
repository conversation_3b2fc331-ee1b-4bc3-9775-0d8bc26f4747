package com.ctrip.dcs.domain.schedule.process.impl;

import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.exception.OrderStatusException;
import com.ctrip.dcs.domain.common.service.ConfirmDspOrderService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.YesOrNo;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.gateway.SelfTransportInventoryGateway;
import com.ctrip.dcs.domain.schedule.process.Process;
import com.ctrip.dcs.domain.schedule.process.Processor;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.service.RecommendService;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

;

/**
 * 系统指派运力组
 * <AUTHOR>
 */
@Processor(value = "systemAssignTransportProcess", type = DspType.DISPATCHER_ASSIGN)
public class SystemAssignTransportProcess extends BaseProcess implements Process {
    private static final Logger logger = LoggerFactory.getLogger(SystemAssignTransportProcess.class);

    @Autowired
    private ScheduleTaskRepository taskRepository;

    @Autowired
    private RecommendService recommendService;

    @Autowired
    private CheckService checkService;

    @Autowired
    private ConfirmDspOrderService confirmDspOrderService;

    @Autowired
    private MessageProviderService messageProducer;
    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private SelfTransportInventoryGateway selfTransportInventoryGateway;

    @Override
    public void execute(ScheduleTaskDO task, DspOrderVO order) {
        boolean confirm = false;
        try {
            // 运力推荐
            List<SortModel> models = recommendService.recommend(order, task.getSubSku(), DuidVO.of(task));
            if (CollectionUtils.isEmpty(models)) {
                logger.warn("system assign transport process", "transport recommend null! task id:{}, dsp order id:{}", task.getTaskId(), task.getDspOrderId());
                recommendMetrics(order);
                return;
            }
            DspOrderVO newOrder = null;
            boolean allConfigDspTypeFlag = queryDspOrderService.getAllConfigDspTypeFlag(task);
            logger.info("system_assign_transport_process_allConfigDspTypeFlag", JsonUtils.toJson(allConfigDspTypeFlag));
            if(allConfigDspTypeFlag){
                newOrder = queryDspOrderService.queryOrderDetail(order.getDspOrderId());
                if(Objects.isNull(newOrder)){
                    logger.error("system assign process", "query order detail fail! task id:{}, dsp order id:{}", task.getTaskId(), task.getDspOrderId());
                    MetricsUtil.recordValue(MetricsConstants.TAKEN_ORDER_DETAIL_NULL);
                    return;
                }
            }
            order = Optional.ofNullable(newOrder).orElse(order);
            if (order.getRatePlanId() != null && order.getRatePlanId() > 0) {
                models = useRatePlanInventory(order, models);
                if (CollectionUtils.isEmpty(models)) {
                    logger.warn("system assign transport process", "ratePlan transport recommend null! task id:{}, dsp order id:{}", task.getTaskId(), task.getDspOrderId());
                    return;
                }
            }
            for (SortModel model : models) {
                try {
                    TransportGroupVO transportGroup = model.getModel().getTransportGroup();
                    // 应单
                    DispatcherConfirmVO confirmVO = DispatcherConfirmVO.builder()
                            .dspOrder(order)
                            .serviceProvider(new ServiceProviderVO(order.getSpId()))
                            .supplier(new SupplierVO(transportGroup.getSupplierId()))
                            .transportGroup(transportGroup)
                            .duid(DuidVO.of(task))
                            .event(OrderStatusEvent.SYSTEM_ASSIGN)
                            .operator(OperatorVO.systemOperator())
                            .build();
                    confirmDspOrderService.confirm(confirmVO);
                    confirm = true;
                    // 成功后退出循环
                    break;
                } catch (OrderStatusException e) {
                    logger.warn(e);
                    if (ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR.equals(e.getErrorCode())) {
                        // 派发单不允许应单异常，则推出。否则，尝试下一个司机。
                        throw ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR.getBizException();
                    }
                } catch (Exception e) {
                    logger.error("transport confirm error", e);
                }
            }
        } catch (Exception e) {
            logger.error(e);
        } finally {
            completeTask(task, confirm);
        }
    }

    public List<SortModel> useRatePlanInventory(DspOrderVO order, List<SortModel> models) {
        List<Long> shuntSupplierIds = models.stream().map(m -> m.getModel().getTransportGroup().getSupplierId()).filter(Objects::nonNull).collect(Collectors.toList());
        Long supplierId = selfTransportInventoryGateway.useRatePlanInventory(order.getCityId(), order.getCategoryCode().getType(), order.getUseDays().days(order.getCategoryCode()), shuntSupplierIds, order.getRatePlanId().longValue(), order.getUserOrderId(), order.getDspOrderId(), order.getSkuId().longValue());
        if (supplierId != null) {
            return models.stream().filter(m -> m.getModel().getTransportGroup().getSupplierId().equals(supplierId)).collect(Collectors.toList());
        }
        return null;
    }

    private void recommendMetrics(DspOrderVO order) {
        String key = String.format(MetricsConstants.TRANSPORT_RECOMMEND_EMPTY_COUNT, order.getCategoryCode().getType());
        MetricsUtil.recordValue(key);
        if (CategoryCodeEnum.isCharterOrder(order.getCategoryCode().getType()) && YesOrNo.NO.getCode() == order.getRushOrder()) {
            MetricsUtil.recordValue(MetricsConstants.CHARTER_ORDER_RECOMMEND_EMPTY_COUNT);
        }
        // 延后派立即确认的订单，如果运力组为空，单独埋点告警
        if (order.isPreConfirm()) {
            logger.warn("assignTransportGroup_failed. oid=" + order.getUserOrderId(), "PreConfirmed order's transportGroup is null, must check it! ");
            MetricsUtil.recordValue(String.format(MetricsConstants.TRANSPORT_RECOMMEND_EMPTY_COUNT, "pre_confirm_order"));
        }
    }
}
