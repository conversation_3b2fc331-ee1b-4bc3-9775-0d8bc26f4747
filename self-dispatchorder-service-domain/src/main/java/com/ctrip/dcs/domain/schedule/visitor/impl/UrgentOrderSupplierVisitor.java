package com.ctrip.dcs.domain.schedule.visitor.impl;

import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class UrgentOrderSupplierVisitor extends AbstractVisitor {

    private SelfOrderQueryGateway selfOrderQueryGateway;

    public UrgentOrderSupplierVisitor() {
        this.selfOrderQueryGateway = getInstance(SelfOrderQueryGateway.class);
    }

    public UrgentOrderSupplierVisitor(SelfOrderQueryGateway selfOrderQueryGateway) {
        this.selfOrderQueryGateway = selfOrderQueryGateway;
    }

    @Override
    public void visit(CheckContext context) {
        Set<Long> supplierIds = context.getUrgentOrderSupplierIds();
        if (CollectionUtils.isNotEmpty(supplierIds)) {
            return;
        }
        List<Long> list = selfOrderQueryGateway.queryDispatcherGrabOrderSupplierIds(true);
        if (CollectionUtils.isNotEmpty(list)) {
            supplierIds.addAll(list);
        }
    }
}
