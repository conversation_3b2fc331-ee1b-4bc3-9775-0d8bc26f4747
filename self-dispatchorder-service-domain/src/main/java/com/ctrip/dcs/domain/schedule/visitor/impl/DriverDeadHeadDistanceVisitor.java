package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.service.DriverDeadHeadDisService;
import com.ctrip.dcs.domain.common.value.DriverLocationVO;
import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.process.impl.SystemAssignProcess;
import com.ctrip.dcs.domain.schedule.value.DeadHeadDisModelVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DriverDeadHeadDistanceVisitor extends AbstractVisitor {


    private DspOrderVO dspOrder;

    private List<DriverVO> drivers;

    private DriverDeadHeadDisService driverDeadHeadDisService;

    private DriverLocationVisitor driverLocationVisitor;

    private DriverRelateOrderVisitor driverRelateOrderVisitor;

    public DriverDeadHeadDistanceVisitor(DspOrderVO dspOrder, List<DriverVO> drivers) {
        Assert.notNull(dspOrder);
        Assert.notEmpty(drivers);
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.driverDeadHeadDisService = getInstance(DriverDeadHeadDisService.class);
        this.driverLocationVisitor = new DriverLocationVisitor(drivers);
        this.driverRelateOrderVisitor = new DriverRelateOrderVisitor(dspOrder, drivers, 12,false);
    }

    public DriverDeadHeadDistanceVisitor(DspOrderVO dspOrder, List<DriverVO> drivers, DriverDeadHeadDisService driverDeadHeadDisService, DriverLocationVisitor driverLocationVisitor, DriverRelateOrderVisitor driverRelateOrderVisitor) {
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.driverDeadHeadDisService = driverDeadHeadDisService;
        this.driverLocationVisitor = driverLocationVisitor;
        this.driverRelateOrderVisitor = driverRelateOrderVisitor;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, DeadHeadDisModelVO> deadHeadDisModelMap = context.getDriverDeadHeadDisModelMap();
        List<DriverVO> list = filter(this.drivers, deadHeadDisModelMap.keySet());
        if (CollectionUtils.isNotEmpty(list)) {
            // 依赖司机点位数据
            driverLocationVisitor.visit(context);
            // 依赖司机相邻单数据
            driverRelateOrderVisitor.visit(context);
            Map<Long, DriverLocationVO> locationMap = context.getDriverLastLocationMap();
            Map<Long, DriverRelateOrderVO> relateOrderMap = context.getContext().getRelateOrderNoEmptyDriveLbsMap();
            Map<Long, DeadHeadDisModelVO> map = driverDeadHeadDisService.calculateDeadHeadDis(dspOrder, list, relateOrderMap, locationMap);
            deadHeadDisModelMap.putAll(map);
        }

    }

}
