package com.ctrip.dcs.domain.schedule;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.ScheduleStatus;
import com.ctrip.dcs.domain.common.enums.ScheduleType;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.entity.DspOrderRewardStrategyDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.value.RewardVO;
import com.ctrip.dcs.domain.schedule.value.ScheduleRecordVO;
import com.ctrip.dcs.domain.schedule.value.ScheduleStrategyVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 调度
 * <AUTHOR>
 */
@Getter
public class ScheduleDO {

    private static final Logger logger = LoggerFactory.getLogger(ScheduleDO.class);

    public static final long DEFAULT_RETRY_SECONDS = 30L;

    public static final String NEXT_EXECUTE_SCHEDULE_TASK_KEY = "NEXT_EXECUTE_SCHEDULE_TASK_%s";

    private Long scheduleId;

    private String dspOrderId;

    private ScheduleStrategyVO strategy;

    private Integer round;

    private ScheduleType type;

    private ScheduleStatus status;

    private Date executeTime;

    private Date shutdownTime;

    private Date createTime;

    private List<ScheduleTaskDO> tasks;

    private List<ScheduleTaskDO> next;

    private List<ScheduleTaskDO> rebuild;

    private List<ScheduleTaskDO> cancel;

    private ScheduleRecordVO record;

    private List<DspOrderRewardStrategyDO> rewardStrategyList;

    public ScheduleDO(Long scheduleId, String dspOrderId, ScheduleStrategyVO strategy, Integer round, ScheduleStatus status, List<ScheduleTaskDO> tasks, Date executeTime, Date shutdownTime, Date createTime) {
        Assert.notBlank(dspOrderId);
        Assert.notNull(strategy);
        Assert.notNull(round);
        Assert.notNull(status);
        this.scheduleId = scheduleId;
        this.dspOrderId = dspOrderId;
        this.strategy = strategy;
        this.round = round;
        this.status = status;
        this.tasks = tasks;
        this.executeTime = executeTime;
        this.shutdownTime = shutdownTime;
        this.createTime = createTime;
    }

    @Builder
    public ScheduleDO(Long scheduleId, String dspOrderId, ScheduleStrategyVO strategy, Integer round, ScheduleType type, ScheduleStatus status, List<ScheduleTaskDO> tasks, Date executeTime, Date shutdownTime, List<DspOrderRewardStrategyDO> rewardStrategyList, Date createTime) {
        Assert.notBlank(dspOrderId);
        Assert.notNull(strategy);
        Assert.notNull(round);
        Assert.notNull(status);
        this.scheduleId = scheduleId;
        this.dspOrderId = dspOrderId;
        this.strategy = strategy;
        this.round = round;
        this.type = type;
        this.status = status;
        this.tasks = tasks;
        this.executeTime = executeTime;
        this.shutdownTime = shutdownTime;
        this.rewardStrategyList = rewardStrategyList;
        this.createTime = createTime;
    }

    /**
     * 执行调度
     * @return 需要执行的任务
     */
    public void execute(DspOrderVO dspOrderVO, List<ScheduleTaskDO> tasks, RewardVO reward) {
        this.tasks = tasks;
        this.next = Collections.emptyList();
        this.rebuild = Collections.emptyList();
        this.executeTime = new Date();
        List<ScheduleTaskDO> list = getWaitExecuteTask();
        if (CollectionUtils.isEmpty(list)) {
            // 重新构建调度
            this.rebuild = rebuild(dspOrderVO, reward);
            list = this.rebuild;
        }
        // 最高优先级任务
        this.next =  getHighestPriorityTask(list);
    }

    /**
     * 终止调度
     * @return 取消的任务
     */
    public void shutdown(List<ScheduleTaskDO> tasks) {
        this.tasks = tasks;
        this.status = ScheduleStatus.SHUTDOWN;
        this.shutdownTime = new Date();
        this.cancel = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tasks)) {
            for (ScheduleTaskDO task : tasks) {
                if (!task.isCancel()) {
                    task.cancel();
                    this.cancel.add(task);
                }
            }
        }
    }

    /**
     * 是否终止
     * @return true-是，false-否
     */
    public boolean isShutdown() {
        return Objects.equals(status, ScheduleStatus.SHUTDOWN);
    }

    /**
     * 调度是否正在执行
     * 1、无正在执行的任务-false
     * 2、有正在执行的任务，并且任务未超时-true
     * 3、有正在执行的任务，并且都超时了-false
     * @return true-正在执行，false-未执行
     */
    public static boolean isExecuting(List<ScheduleTaskDO> list) {
        List<ScheduleTaskDO> tasks = getExecuteTask(list);
        if (CollectionUtils.isNotEmpty(tasks)) {
            // 所有正在执行的任务，都超时了
            return !tasks.stream().allMatch(ScheduleTaskDO::isTimeout);
        }
        return false;
    }

    /**
     * 是否超时
     * 当前时间与订单最晚确认时间比较
     * @return true-当前时间晚于最晚确认时间，false-当前时间早于最晚确认时间
     */
    public boolean isTimeout(DspOrderVO order) {
        return new Date().after(order.getLastConfirmTimeBj());
    }

    /**
     * 待执行的任务
     * @return
     */
    public List<ScheduleTaskDO> getWaitExecuteTask() {
        return tasks.stream().filter(ScheduleTaskDO::isWaitExecute).collect(Collectors.toList());
    }

    public static List<ScheduleTaskDO> getWaitExecuteTask(List<ScheduleTaskDO> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            return Collections.emptyList();
        }
        return tasks.stream().filter(ScheduleTaskDO::isWaitExecute).collect(Collectors.toList());
    }


    public static List<ScheduleTaskDO> getExecuteTask(List<ScheduleTaskDO> tasks) {
        return tasks.stream().filter(ScheduleTaskDO::isExecute).collect(Collectors.toList());
    }

    /**
     * 调度重试时间
     * @return
     */
    public Long retry(List<ScheduleTaskDO> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            return null;
        }
        // 默认30秒
        Long retry = null;
        for (ScheduleTaskDO task : tasks) {
            if (task.isCancel() || task.getSubSku() == null || task.getSubSku().getRetrySecond() == null || task.getSubSku().getRetrySecond() <= 0) {
               continue;
            }
            Long subSkuRetry = task.getSubSku().getRoundRetrySecond(task.getRound());
            retry = retry == null ? subSkuRetry : Math.min(retry, subSkuRetry);
        }
        if (retry == null || retry < DEFAULT_RETRY_SECONDS) {
            retry = DEFAULT_RETRY_SECONDS;
            MetricsUtil.recordValue(MetricsConstants.DSP_SCHEDULE_RETRY_SECONDS_LESS_30_COUNT);
        }
        logger.info("ScheduleTaskRetryInfo", "schedule retry time is {}, schedule:{}", retry, this.scheduleId);
        return Double.valueOf(retry).longValue();
    }

    /**
     * 重新构建调度
     * @return
     */
    private List<ScheduleTaskDO> rebuild(DspOrderVO dspOrder, RewardVO reward) {
        // 调度增加轮次
        addRound();
        Long retry = retry(tasks);
        // 构建待执行任务
        this.tasks.forEach(t -> t.waitExecute(dspOrder, reward, ObjectUtils.defaultIfNull(retry, 0L).intValue()));
        return getWaitExecuteTask();
    }

    /**
     * 最高优先级的任务
     * @param list
     * @return
     */
    public static List<ScheduleTaskDO> getHighestPriorityTask(List<ScheduleTaskDO> list) {
        List<ScheduleTaskDO> result = Lists.newArrayList();
        int max = Integer.MIN_VALUE;
        for (ScheduleTaskDO task : list) {
            if (task.getPriority() < max) {
                continue;
            }
            if (task.getPriority() > max) {
                max = task.getPriority();
                result = Lists.newArrayList();
            }
            result.add(task);
        }
        return result;
    }

    /**
     * 增加调度轮次
     */
    private void addRound() {
        round++;
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public boolean isVBKType() {
        return ScheduleType.isVBKType(this.getType());
    }
}
