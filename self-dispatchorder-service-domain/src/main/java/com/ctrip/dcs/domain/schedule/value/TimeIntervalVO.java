package com.ctrip.dcs.domain.schedule.value;

import com.ctrip.dcs.domain.common.value.EstimatedUseTimeVO;
import com.ctrip.dcs.domain.common.value.PeriodVO;
import com.ctrip.dcs.domain.common.value.PeriodsVO;
import com.ctrip.dcs.domain.common.value.PredictServiceStopTimeVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 时间间隔
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public class TimeIntervalVO {

    private EstimatedUseTimeVO estimatedUseTime;

    private PredictServiceStopTimeVO predictServiceStopTime;

    private PeriodVO period;

    public TimeIntervalVO(EstimatedUseTimeVO estimatedUseTime, PredictServiceStopTimeVO predictServiceStopTime, PeriodsVO periods) {
        this.estimatedUseTime = estimatedUseTime;
        this.predictServiceStopTime = predictServiceStopTime;
        this.period = periods.getPeriods().stream().filter(periodVO -> periods.contains(estimatedUseTime.getTimeInt())).findFirst().orElse(null);
    }

    /**
     * 与前向单完成时间的时间间隔
     * @param prev
     * @param defaultInterval
     * @return
     */
    public int interval(PredictServiceStopTimeVO prev, int defaultInterval) {
        if (Objects.nonNull(prev)) {
            // 有前向单
            return prev.interval(this.estimatedUseTime);
        }
        if (Objects.nonNull(period)) {
            // 命中的工作时段
            return period.getStart().interval(this.estimatedUseTime.getTimeInt());
        }
        return defaultInterval;
    }

    /**
     * 与后向但开始时间间隔
     * @param after
     * @param defaultInterval
     * @return
     */
    public int interval(EstimatedUseTimeVO after, int defaultInterval) {
        if (Objects.nonNull(after)) {
            // 有后向单
            return this.predictServiceStopTime.interval(after);
        }
        if (Objects.nonNull(period)) {
            // 命中的工作时段
            return period.getEnd().interval(period.getStart(), this.predictServiceStopTime.getTimeInt());
        }
        return defaultInterval;
    }
}
