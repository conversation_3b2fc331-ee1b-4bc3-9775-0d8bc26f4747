package com.ctrip.dcs.domain.schedule.process.impl;

import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.GrabOrderCode;
import com.ctrip.dcs.domain.common.exception.OrderStatusException;
import com.ctrip.dcs.domain.common.exception.SlaveOrderStatusException;
import com.ctrip.dcs.domain.common.service.*;
import com.ctrip.dcs.domain.common.util.CatUtil;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.factory.BatchConfirmDspOrderFactory;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.dcs.domain.schedule.check.CheckCode;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;
import com.ctrip.dcs.domain.schedule.event.GrabOrderFailEvent;
import com.ctrip.dcs.domain.schedule.event.GrabOrderSuccessEvent;
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory;
import com.ctrip.dcs.domain.schedule.gateway.ConflictGateway;
import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.domain.schedule.repository.SelectGrabOrderRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.service.RecommendService;
import com.ctrip.dcs.domain.schedule.service.SortService;
import com.ctrip.dcs.domain.schedule.value.GrabOrderResultVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.dianping.cat.Cat;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

;

/**
 * 抢单轮选
 * <AUTHOR>
 */
@Component
public class SelectGrabOrderProcess extends BaseProcess {

    private static final Logger logger = LoggerFactory.getLogger(SelectGrabOrderProcess.class);

    @Autowired
    private SelectGrabOrderRepository selectGrabOrderRepository;

    @Autowired
    private QueryDspOrderService dspOrderService;

    @Autowired
    private ScheduleTaskRepository scheduleTaskRepository;

    @Autowired
    private RecommendService recommendService;

    @Autowired
    private SortService sortService;

    @Autowired
    private CheckService checkService;

    @Autowired
    private BatchConfirmDspOrderFactory dspOrderGroupFactory;

    @Autowired
    private DriverOrderFactory driverOrderFactory;

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    @Qualifier("broadcastGrabConfig")
    private ConfigService broadcastGrabConfig;

    @Autowired
    private ConfirmDspOrderService confirmDspOrderService;

    @Autowired
    private ConflictGateway conflictGateway;

    @Autowired
    private QueryVehicleService queryVehicleService;
    @Resource
    SysSwitchConfigGateway sysSwitchConfigGateway;

    public void execute(DspOrderVO dspOrder, SubSkuVO subSku) {
        // 查询已抢订单
        List<GrabOrderDO> grabOrders = selectGrabOrderRepository.find(dspOrder.getDspOrderId(), subSku.getSubSkuId());
        if (CollectionUtils.isEmpty(grabOrders)) {
            return;
        }
        List<GrabOrderResultVO> results = grabOrders.stream().map(GrabOrderResultVO::new).collect(Collectors.toList());
        try {
            if (!dspOrder.isDispatching()) {
                return;
            }
            Set<Long> driverIds = grabOrders.stream().map(GrabOrderDO::getDriverId).collect(Collectors.toSet());
            logger.info("SelectGrabOrderProcess_content", "dsp order id:{}, driver ids:{}", dspOrder.getDspOrderId(), JsonUtils.toJson(driverIds));
            // 查询司机
            Long supplierId = dspOrder.getSupplierId() != null ? dspOrder.getSupplierId().longValue() : null;
            List<DriverVO> drivers = queryDriverService.queryDriver(driverIds, CategoryUtils.selfGetParentType(dspOrder), supplierId);
            // 排序
            drivers = sortService.sort(dspOrder, subSku, drivers);
            Map<Long /* driver id */, GrabOrderResultVO> map = results.stream().collect(Collectors.toMap(o -> o.getGrabOrder().getDriverId(), o -> o, (o1, o2) -> o2));
            for (DriverVO driver : drivers) {
                Long driverId = driver.getDriverId();
                GrabOrderResultVO result = map.get(driverId);
                if (Objects.isNull(result)) {
                    continue;
                }
                GrabOrderDO grabOrder = result.getGrabOrder();
                // 组合应单
                GrabOrderCode code = confirm(dspOrder, driver, subSku, grabOrder);
                result.setCode(code);
                if (result.getCode().isSuccess()) {
                    // 司机应单成功
                    break;
                }
            }
        } catch (Exception e) {
            logger.error(e);
        } finally {
            // 通知抢单结果
            notice(results);
            // 清空轮选信息
            clear(dspOrder, subSku);
        }
    }

    /**
     * 组合应单
     * @param masterDspOrder
     * @param subSku
     * @param grabOrder
     * @return
     */
    private GrabOrderCode confirm(DspOrderVO masterDspOrder, DriverVO driver, SubSkuVO subSku, GrabOrderDO grabOrder) {
        // 查询子单
        DspOrderVO slaveDspOrder = null;
        if (grabOrder.isGroup()) {
            Cat.logEvent("selectGrabOrderProcess.confirm.isgroup","1");
            slaveDspOrder = dspOrderService.query(grabOrder.getSubGrabOrder().getDspOrderId());
        }
        // 组合订单接单检查
        CheckModel checkModel = check(subSku, driver, masterDspOrder, slaveDspOrder, DuidVO.of(grabOrder.getDuid()));
        if (!checkModel.isPass()) {
            logger.info("select grab process", "driver taken check fail! dsp order id:{}, driver id:{}", grabOrder.getDspOrderId(), driver.getDriverId());
            return GrabOrderCode.valueOf(checkModel.getCheckCode());
        }
        TransportGroupVO transportGroup = checkModel.getModel().getTransportGroup();
        BatchConfirmDspOrderVO batchConfirmDspOrder = dspOrderGroupFactory.create(grabOrder, masterDspOrder, slaveDspOrder, driver, transportGroup);
        if (Objects.isNull(batchConfirmDspOrder)) {
            logger.info("select grab process", "group order create fail! dsp order id:{}, driver id:{}", masterDspOrder.getDspOrderId(), driver.getDriverId());
            return GrabOrderCode.ORDER_TAKEN_FAIL;
        }
        VehicleVO vehicle = queryVehicle(driver,CategoryUtils.selfGetParentType(masterDspOrder));
        try {
            // 应单
            BatchDriverAndCarConfirmVO confirmVO = BatchDriverAndCarConfirmVO.builder()
                    .batchDspOrder(batchConfirmDspOrder)
                    .serviceProvider(new ServiceProviderVO(masterDspOrder.getSpId()))
                    .supplier(driver.getSupplier())
                    .transportGroup(transportGroup)
                    .driver(driver)
                    .vehicle(vehicle)
                    .duid(DuidVO.of(grabOrder.getDuid()))
                    .event(OrderStatusEvent.SYSTEM_ASSIGN)
                    .build();
            confirmDspOrderService.confirm(confirmVO);
            return GrabOrderCode.SUCCESS;
        } catch (OrderStatusException e) {
            logger.warn(e);
            if (e instanceof SlaveOrderStatusException) {
                // 子单抢单失败
                return GrabOrderCode.PREMIUM_ORDER_TAKEN_FAIL;
            }
            sendDriverOrderConfirmFailMessage(batchConfirmDspOrder, driver);
        } catch (Exception e) {
            logger.error(e);
            sendDriverOrderConfirmFailMessage(batchConfirmDspOrder, driver);
        }
        return GrabOrderCode.ORDER_TAKEN_FAIL;
    }



    private CheckModel check(SubSkuVO subSku, DriverVO driver, DspOrderVO master, DspOrderVO slave, DuidVO duid) {
        // 主单接单检查
        CheckModel check = checkService.check(new TakenCheckCommand(master, subSku, driver, duid));
        if (!check.isPass()) {
            logger.info("select grab process", "master driver taken check fail! dsp order id:{}, driver id:{}", master.getDspOrderId(), driver.getDriverId());
            return check;
        }
        if (Objects.nonNull(slave)) {
            // 主单与子单是否冲突
            Boolean conflict = conflictGateway.checkOrderConflict(master, slave, true);
            if (conflict) {
                logger.info("select grab process", "master driver taken check fail! dsp order id:{}, driver id:{}", master.getDspOrderId(), driver.getDriverId());
                check.setCheckCode(CheckCode.BOOK_TIME_CONFLICT_STATUS);
                return check;
            }
            // 子单接单检查
            check = checkService.check( new TakenCheckCommand(slave, subSku, driver, duid));
            if (!check.isPass()) {
                logger.info("select grab process", "slave driver taken check fail! dsp order id:{}, driver id:{}", slave.getDspOrderId(), driver.getDriverId());
                return check;
            }
        }
        return check;
    }

    /**
     * 抢单结果通知
     * @param results
     */
    public void notice(List<GrabOrderResultVO> results) {
        try {
            // 抢单结果映射
            Map<String /*code*/, String /*desc*/> mapping = broadcastGrabConfig.getMap(ConfigKey.BROADCAST_GRAB_RESULT_KEY);
            boolean driverQmqAddUcsSwitch = sysSwitchConfigGateway.getDriverQmqAddUcsSwitch();
            Map<Long, DriverUdlVO> drvUdlMap = null;
            if (driverQmqAddUcsSwitch) {
                Set<Long> driverIds = results.stream().filter(t -> !t.getCode().isSuccess()).map(GrabOrderResultVO::getGrabOrder).map(GrabOrderDO::getDriverId).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(driverIds)) {
                    drvUdlMap = queryDriverService.getDrvUdlMap(driverIds);
                }
            }
            for (GrabOrderResultVO result : results) {
                GrabOrderDO grabOrder = result.getGrabOrder();
                Long driverId = grabOrder.getDriverId();
                GrabOrderCode code = result.getCode();
                if (code.isSuccess()) {
                    // 司机端不消费抢单成功消息了
                    // 抢单成功消息
//                    messageProducer.send(new GrabOrderSuccessEvent(driverId, grabOrder.getDuid(), grabOrder.getDriverOrderId(), grabOrder.getUserOrderId(), code.getCode(), mapping.getOrDefault(code.getCode(), StringUtils.EMPTY)));
                    continue;
                }
                // 抢单失败消息
                if (!driverQmqAddUcsSwitch) {
                    messageProducer.send(new GrabOrderFailEvent(driverId, grabOrder.getDuid(), grabOrder.getDspOrderId(), grabOrder.getUserOrderId(), code.getCode(), mapping.get(code.getCode())));
                    continue;
                }
                DriverUdlVO driverUdlVO = Optional.ofNullable(drvUdlMap).map(t -> t.get(driverId)).orElse(new DriverUdlVO());
                CatUtil.doWithUdlOverride(() -> {
                    messageProducer.send(new GrabOrderFailEvent(driverId, grabOrder.getDuid(), grabOrder.getDspOrderId(), grabOrder.getUserOrderId(), code.getCode(), mapping.get(code.getCode())));
                    return Boolean.TRUE;
                }, driverUdlVO.getUdl(), driverUdlVO.getRequestFrom());
            }
        } catch (Exception e) {
            logger.error(e);
        }
    }

    /**
     * 清空轮选缓存
     * @param dspOrder
     * @param subSku
     */
    public void clear(DspOrderVO dspOrder, SubSkuVO subSku) {
        selectGrabOrderRepository.delete(dspOrder.getDspOrderId(), subSku.getSubSkuId());
    }


    public VehicleVO queryVehicle(DriverVO driver, ParentCategoryEnum parentCategoryCodeEnum) {
        if (driver == null || driver.getCar() == null || driver.getCar().getCarId() == null) {
            throw ErrorCode.ABSENT_VEHICLE_EXCEPTION.getBizException();
        }
        return queryVehicleService.query(driver.getCar().getCarId(),parentCategoryCodeEnum);
    }
}
