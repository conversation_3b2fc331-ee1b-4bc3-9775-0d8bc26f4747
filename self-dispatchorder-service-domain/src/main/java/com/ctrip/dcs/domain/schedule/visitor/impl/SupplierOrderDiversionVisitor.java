package com.ctrip.dcs.domain.schedule.visitor.impl;

import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.schedule.gateway.SupplierOrderDiversionGateway;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.value.SupplierDiversionVO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class SupplierOrderDiversionVisitor extends AbstractVisitor {

    private DspOrderVO dspOrder;

    private List<TransportGroupVO> transportGroups;

    private SupplierOrderDiversionGateway supplierOrderDiversionGateway;

    public SupplierOrderDiversionVisitor(DspOrderVO dspOrder, List<TransportGroupVO> transportGroups) {
        this.dspOrder = dspOrder;
        this.transportGroups = transportGroups;
        this.supplierOrderDiversionGateway = getInstance(SupplierOrderDiversionGateway.class);
    }

    public SupplierOrderDiversionVisitor(DspOrderVO dspOrder, List<TransportGroupVO> transportGroups, SupplierOrderDiversionGateway supplierOrderDiversionGateway) {
        this.dspOrder = dspOrder;
        this.transportGroups = transportGroups;
        this.supplierOrderDiversionGateway = supplierOrderDiversionGateway;
    }

    @Override
    public void visit(SortContext context) {
        if (MapUtils.isNotEmpty(context.getSupplierOrderDiversionMap())) {
            return;
        }
        Set<Long> supplierIds = transportGroups.stream().map(TransportGroupVO::getSupplierId).collect(Collectors.toSet());
        Integer ratePlanId = dspOrder.getRatePlanId() == null ? 0 : dspOrder.getRatePlanId();
        List<SupplierDiversionVO> list = supplierOrderDiversionGateway.query(dspOrder.getCityId(), dspOrder.getCategoryCode().getType(), dspOrder.getEstimatedUseTime(),dspOrder.getEstimatedUseTimeBj(), Lists.newArrayList(supplierIds),ratePlanId);
        Map<AbstractMap.SimpleEntry<Integer, Integer>, SupplierDiversionVO> countMap = new HashMap<>();
        for (SupplierDiversionVO dto : list) {
            countMap.put(new AbstractMap.SimpleEntry<>(dto.getSupplierId().intValue(), ratePlanId), dto);
        }
        context.getSupplierOrderDiversionMap().putAll(countMap);
    }
}
