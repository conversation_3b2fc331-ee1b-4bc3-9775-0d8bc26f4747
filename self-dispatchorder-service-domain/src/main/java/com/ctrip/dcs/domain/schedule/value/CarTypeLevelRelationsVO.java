package com.ctrip.dcs.domain.schedule.value;

import com.ctrip.dcs.domain.common.enums.CarTypeLevelRelation;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Collections;
import java.util.Map;

public class CarTypeLevelRelationsVO {

    /**
     * 该map中存储了指定车型ID相对于其他车型的等级关系
     * 举例：使用118请求后，该map中存储如下
     * {117, 1} - 118相对于117的关系是升级,
     * {118, 0} - 118相对于118的关系是平级,
     * {119,-1} - 118相对于119的关系是降级
     * <p>
     * key=对比车型ID，value=指定车型->对比车型的等级关系
     */
    private final Map<Long, CarTypeLevelRelation> otherCarTypeRelationMap;

    public CarTypeLevelRelationsVO(Map<Long, CarTypeLevelRelation> otherCarTypeRelationMap) {
        this.otherCarTypeRelationMap = ObjectUtils.defaultIfNull(otherCarTypeRelationMap, Collections.emptyMap());
    }

    /**
     * 查询指定车型->对比车型的等级关系
     *
     * @param otherCarType 对比车型ID
     * @return 指定车型->对比车型的等级关系
     */
    public CarTypeLevelRelation otherCarTypeRelation(Long otherCarType) {
        // 默认订单的车型高于司机的车型
        return otherCarTypeRelationMap.getOrDefault(otherCarType, CarTypeLevelRelation.UNKOWN);
    }

    public Map<Long, CarTypeLevelRelation> getOtherCarTypeRelationMap() {
        return otherCarTypeRelationMap;
    }
}
