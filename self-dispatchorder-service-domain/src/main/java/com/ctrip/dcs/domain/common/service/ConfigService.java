package com.ctrip.dcs.domain.common.service;

import java.util.Map;

/**
 * Qconfig通用服务
 * <AUTHOR>
 */
public interface ConfigService {

    /**
     * 读取配置
     * @param key
     * @return
     */
    String getString(String key);

    String getString(String key, String defaultValue);

    /**
     * 读取配置
     * @param key
     * @return
     */
    Integer getInteger(String key);

    Integer getInteger(String key, Integer defaultValue);

    /**
     * 读取配置
     * @param key
     * @return
     */
    Long getLong(String key);
    Long getLong(String key, Long defaultValue);

    /**
     * 读取配置
     * @param key
     * @return
     */
    Double getDouble(String key);

    Double getDouble(String key, Double defaultValue);

    Map<String, String> getMap(String key);
}
