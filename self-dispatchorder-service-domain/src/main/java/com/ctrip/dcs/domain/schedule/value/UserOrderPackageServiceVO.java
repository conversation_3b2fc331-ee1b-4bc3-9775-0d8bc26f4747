package com.ctrip.dcs.domain.schedule.value;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2023/3/15 15:25
 */
@Getter
@Setter
public class UserOrderPackageServiceVO {
    public Long drvId;
    public String xCategoryCode;
    public Long xSkuId;
    public Integer quantity;
    public BigDecimal supplierTotalPrice;
    public String supplierCurrency;
    public Integer firstFree;
    public BigDecimal unitPrice;
    public String xProductName;
    public String supplierCategoryCode;
    public BigDecimal userTotalPrice;
    public String userCurrency;

}
