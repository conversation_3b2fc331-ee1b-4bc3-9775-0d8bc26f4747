package com.ctrip.dcs.domain.schedule.gateway;


import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TrafficControlGateway {

   /**
    *
    * @param orderInfo
    * @param orderInfo
    * @return drivers
    */
   List<String> queryDriverTrafficControl(Integer cityId, DspOrderVO orderInfo, List<DriverVO> drivers);

}
