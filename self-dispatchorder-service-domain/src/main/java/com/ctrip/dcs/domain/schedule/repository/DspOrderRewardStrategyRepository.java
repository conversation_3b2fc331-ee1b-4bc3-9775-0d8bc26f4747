package com.ctrip.dcs.domain.schedule.repository;

import com.ctrip.dcs.domain.schedule.entity.DspOrderRewardStrategyDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DspOrderRewardStrategyRepository {

    void save(List<DspOrderRewardStrategyDO> rewardStrategyList);

    void update(List<DspOrderRewardStrategyDO> rewardStrategyList);

    List<DspOrderRewardStrategyDO> query(String dspOrderId, Long scheduleId);

    List<DspOrderRewardStrategyDO> queryByRewardTime(Date begin, Integer size);

    DspOrderRewardStrategyDO find(Long strategyId);
}
