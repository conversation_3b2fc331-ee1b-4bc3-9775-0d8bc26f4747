package com.ctrip.dcs.domain.common.value;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Getter
@Setter
public class CarVO {

    /**
     * 车辆id
     */
    public Long carId;

    /**
     * 车牌号
     */
    public String carLicense;

    /**
     * 车辆颜色id
     */
    public Long carColorId;

    /**
     * 车型颜色
     */
    private String carColor;

    /**
     * 车辆品牌id
     */
    public Long carBrandId;

    /**
     * 车辆品牌名称
     */
    private String carBrandName;

    /**
     * 车辆类型id
     */
    public Long carTypeId;

    public String carTypeName;

    /**
     * 车辆车系id
     */
    public Long carSeriesId;

    private String carSeriesName;

    /**
     *
     * *是否是新能源 0-非新能源，1-新能源
     */
    public Integer isEnergy;

    /**
     * 车辆状态  0.未激活,1.上线.3.下线
     */
    private Integer vehicleStatus;


    private String overAgeTime;

}
