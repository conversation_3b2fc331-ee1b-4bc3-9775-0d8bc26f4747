package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.service.DriverDeadHeadDisService;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.DriverLocationVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.value.DeadHeadDisModelVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;


/**
 * 获取前后单并计算空驶
 */
public class DriverEmptyDrivingInfoV2Visitor extends AbstractVisitor {
    private static final Logger logger = LoggerFactory.getLogger(DriverEmptyDrivingInfoV2Visitor.class);

    private DspOrderVO dspOrder;

    private List<DriverVO> drivers;

    private DriverDeadHeadDisService driverDeadHeadDisService;

    private DriverLocationVisitor driverLocationVisitor;

    private DriverForwardAndBackwardOrderVisitor driverForwardAndBackwardOrderVisitor;

    public DriverEmptyDrivingInfoV2Visitor(DspOrderVO dspOrder, List<DriverVO> drivers) {
        Assert.notNull(dspOrder);
        Assert.notEmpty(drivers);
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.driverDeadHeadDisService = getInstance(DriverDeadHeadDisService.class);
        this.driverLocationVisitor = new DriverLocationVisitor(drivers);
        this.driverForwardAndBackwardOrderVisitor = new DriverForwardAndBackwardOrderVisitor(dspOrder, drivers);
    }

    // for UT only
    protected DriverEmptyDrivingInfoV2Visitor(DspOrderVO dspOrder, List<DriverVO> drivers, DriverDeadHeadDisService driverDeadHeadDisService, DriverLocationVisitor driverLocationVisitor, DriverForwardAndBackwardOrderVisitor driverForwardAndBackwardOrderVisitor) {
        Assert.notNull(dspOrder);
        Assert.notEmpty(drivers);
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.driverDeadHeadDisService = driverDeadHeadDisService;
        this.driverLocationVisitor = driverLocationVisitor;
        this.driverForwardAndBackwardOrderVisitor = driverForwardAndBackwardOrderVisitor;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, DeadHeadDisModelVO> map = context.getDriverEmptyDrivingInfo4OrderMileageV2Map();
        List<DriverVO> filter = filter(drivers, map.keySet());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        // 获取司机定位
        driverLocationVisitor.visit(context);
        // 获取司机前后单
        driverForwardAndBackwardOrderVisitor.visit(context);
        // 查询lbs 计算空驶
        Map<Long, DeadHeadDisModelVO> result = driverDeadHeadDisService.calculateDeadHeadDisForOrderMileage(dspOrder, drivers, context.getDriverLastLocationMap(), context.getContext().getDriverForwardOrderMap(), context.getContext().getDriverBackwardOrderMap());
        map.putAll(result);
        logger.info("DriverEmptyDrivingInfoV2Visitor", JsonUtil.toJson(context));
    }

    @Override
    public void visit(SortContext context) {
        Map<Long, DeadHeadDisModelVO> map = context.getDriverEmptyDrivingInfo4OrderMileageV2Map();
        List<DriverVO> filter = filter(drivers, map.keySet());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        // 获取司机定位
        driverLocationVisitor.visit(context);
        // 获取司机前后单
        driverForwardAndBackwardOrderVisitor.visit(context);
        Map<Long, DriverLocationVO> driverLastLocationMap = Maps.newHashMap();
        // 查询lbs 计算空驶
        Map<Long, DeadHeadDisModelVO> result = driverDeadHeadDisService.calculateDeadHeadDisForOrderMileage(dspOrder, drivers, driverLastLocationMap, context.getDspContext().getDriverForwardOrderMap(), context.getDspContext().getDriverBackwardOrderMap());
        map.putAll(result);
        logger.info("DriverEmptyDrivingInfoV2Visitor", JsonUtil.toJson(context));
    }
}
