package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDetailDO;
import com.ctrip.dcs.domain.schedule.dto.DrvOrderRewardVO;
import com.ctrip.dcs.domain.schedule.value.PerformanceConfigVO;
import com.ctrip.dcs.domain.schedule.value.RedispatchOrderVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DriverOrderGateway {

    String createForModify(DspOrderVO order, DriverVO driver, CarVO car, TransportGroupVO transportGroup, SupplierVO supplier,
                  ServiceProviderVO serviceProvider, BigDecimal rewardAmount, List<XproductVO> xproductList,
                  Boolean separateOrder, Integer isSelfDriver,String dispatchType, Integer checkCode, List<DrvOrderRewardVO> driverOrderRewardList,
                           Long oldDriverId,
                           String oldDriverOrderId,
                           Integer modifyVersion,
                           Integer dspType
    );

    String create(DspOrderVO order, DriverVO driver, CarVO car, TransportGroupVO transportGroup, SupplierVO supplier,
                  ServiceProviderVO serviceProvider, BigDecimal rewardAmount, List<XproductVO> xproductList,
                  Boolean separateOrder, Integer isSelfDriver,String dispatchType, Integer checkCode, List<DrvOrderRewardVO> driverOrderRewardList,
                  Integer dspType);

    String create(DspOrderVO order, DriverVO driver, CarVO car, TransportGroupVO transportGroup, SupplierVO supplier,
                  ServiceProviderVO serviceProvider, BigDecimal rewardAmount, List<XproductVO> xproductList,
                  Boolean separateOrder, Integer isSelfDriver, String dispatchType, BigDecimal vbkInitialAmount, BigDecimal vbkRewardAmount,
                  String vbkInitialAmountCurrency, String vbkRewardAmountCurrency, Integer checkCode, List<DrvOrderRewardVO> driverOrderRewardList,
                  RedispatchOrderVO redispatchOrderVO,
                  Integer dspType);

    String createForSaaS(DspOrderVO order, DriverVO driver, CarVO car, TransportGroupVO transportGroup, SupplierVO supplier,
                  ServiceProviderVO serviceProvider, BigDecimal rewardAmount, List<XproductVO> xproductList,
                  Boolean separateOrder, Integer isSelfDriver,boolean newProcess, Integer checkCode, List<DrvOrderRewardVO> driverOrderRewardList,
                         Integer dspType);

    void cancel(String dspOrderId, String driverOrderId, Long driverId, Integer cancelRole, Integer cancelCauseCode, String cancelReason, Boolean forcedCancel,Boolean needHoldOnDriverOrder);

    void confirmCar(String dspOrderId, String driverOrderId, Long driverId, VehicleVO vehicle);

    Boolean canApplyAppointment(String driverOrderId, Long driverId);

    void update(DspOrderDO oldDspOrderDO, DspOrderDO newDspOrderDO,
                DspOrderDetailDO oldSDspOrderDetailDO, DspOrderDetailDO newSDspOrderDetailDO, Integer newProcess);
    
    void updateOrderRemark(DspOrderDO dspOrderDO ,String oldDriverRemark, String newDriverRemark);

    void saasCancel(String dspOrderId, String driverOrderId, Long driverId, Integer cancelRole, Integer cancelReasonId, String cancelReason);
    
    PerformanceConfigVO queryPerformanceConfig(Integer supplierId);
    
}
