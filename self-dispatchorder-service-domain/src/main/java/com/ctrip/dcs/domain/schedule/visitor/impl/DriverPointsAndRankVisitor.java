package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.DriverPointsGateway;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DriverPointsAndRankVisitor extends AbstractVisitor {

    private List<DriverVO> drivers;

    private Long cityId;

    private DriverPointsGateway dcsDriverLevelServiceGateway;

    public DriverPointsAndRankVisitor(List<DriverVO> drivers, Long cityId) {
        Assert.notEmpty(drivers);
        Assert.notNull(cityId);
        this.drivers = drivers;
        this.cityId = cityId;
        this.dcsDriverLevelServiceGateway = getInstance(DriverPointsGateway.class);
    }

    public DriverPointsAndRankVisitor(List<DriverVO> drivers, Long cityId, DriverPointsGateway dcsDriverLevelServiceGateway) {
        this.drivers = drivers;
        this.cityId = cityId;
        this.dcsDriverLevelServiceGateway = dcsDriverLevelServiceGateway;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, DriverPointsVO> pointsMap = context.getContext().getDriverPointsMap();
        visit(pointsMap);
    }

    @Override
    public void visit(SortContext context) {
        Map<Long, DriverPointsVO> pointsMap = context.getDspContext().getDriverPointsMap();
        visit(pointsMap);
    }

    private void visit(Map<Long, DriverPointsVO> pointsMap) {
        List<DriverVO> filter = filter(drivers, pointsMap.keySet());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        List<Long> driverIds = filter.stream().map(DriverVO::getDriverId).collect(Collectors.toList());
        List<DriverPointsVO> points = dcsDriverLevelServiceGateway.batchQueryDriverTotalPointAndRank(driverIds, cityId);
        if (CollectionUtils.isEmpty(points)) {
            return;
        }
        for (DriverPointsVO point : points) {
            pointsMap.put(point.getDriverId(), point);
        }
    }
}
