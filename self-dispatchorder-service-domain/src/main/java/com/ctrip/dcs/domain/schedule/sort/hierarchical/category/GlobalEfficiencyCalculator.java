package com.ctrip.dcs.domain.schedule.sort.hierarchical.category;

import com.ctrip.dcs.domain.common.value.DriverMileageProfitVO;
import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.EnhancedNormalizer;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.NormalizationContext;
import com.ctrip.dcs.domain.schedule.value.DriverAttendanceVO;
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局效率类计算器
 * 
 * 负责计算全局效率相关的特征：
 * 1. REVENUE_BALANCE - 收益均衡 (对应原F13司机日收益)
 * 2. REGIONAL_DISPATCH - 区域调度优化
 * 3. PEAK_STRATEGY - 高峰时段策略
 * 4. FUTURE_ORDER_CAPACITY - 未来接单能力 (对应原F11)
 * 5. DRIVER_TIER - 司机分层 (对应原F19)
 * 
 * <AUTHOR>
 */
public class GlobalEfficiencyCalculator implements CategoryCalculator {

    private static final Logger logger = LoggerFactory.getLogger(GlobalEfficiencyCalculator.class);
    
    private static final int ABSENCE_DAY_THRESHOLD = 15;
    private static final int TIME_INTERVAL_THRESHOLD = 105 * 60; // 105分钟转秒
    
    private final HierarchicalSortConfig config;
    private final List<String> subItems;

    public GlobalEfficiencyCalculator(HierarchicalSortConfig config) {
        this.config = config;
        this.subItems = Arrays.asList(
            "REVENUE_BALANCE",
            "REGIONAL_DISPATCH", 
            "PEAK_STRATEGY",
            "FUTURE_ORDER_CAPACITY",
            "DRIVER_TIER"
        );
    }

    @Override
    public Map<String, Double> calculate(List<SortModel> models, SortContext context) {
        if (models == null || models.isEmpty()) {
            return new HashMap<>();
        }

        try {
            // 1. 计算各子项分数
            Map<String, Map<String, Double>> subItemScores = new HashMap<>();
            
            subItemScores.put("REVENUE_BALANCE", calculateRevenueBalance(models, context));
            subItemScores.put("REGIONAL_DISPATCH", calculateRegionalDispatch(models, context));
            subItemScores.put("PEAK_STRATEGY", calculatePeakStrategy(models, context));
            subItemScores.put("FUTURE_ORDER_CAPACITY", calculateFutureOrderCapacity(models, context));
            subItemScores.put("DRIVER_TIER", calculateDriverTier(models, context));

            // 2. 获取子项权重
            Map<String, Double> subItemWeights = getSubItemWeights();

            // 3. 聚合为类别分数
            return aggregateSubItemScores(subItemScores, subItemWeights);

        } catch (Exception e) {
            logger.error("Failed to calculate global efficiency scores", e);
            return createFallbackScores(models);
        }
    }

    /**
     * 计算收益均衡分数 (对应原F13司机日收益特征)
     */
    private Map<String, Double> calculateRevenueBalance(List<SortModel> models, SortContext context) {
        Map<String, Double> scores = new HashMap<>();
        List<Double> rawScores = models.stream()
            .map(model -> {
                try {
                    DriverVO driver = model.getModel().getDriver();
                    DspOrderVO order = model.getModel().getOrder();
                    
                    // 获取司机日收益和基线收益
                    DriverMileageProfitVO todayProfit = context.getTodayDriverMileageProfit(driver.getDriverId());
                    Integer baselineDay = context.getProfitBaselineDay(order.getCityId(), driver.getCar().getCarTypeId());
                    
                    if (todayProfit == null || baselineDay == null || baselineDay <= 0) {
                        return 0.0;
                    }
                    
                    double driverDayRevenue = todayProfit.getProfit() != null ? todayProfit.getProfit() : 0.0;
                    
                    // 实现原F13逻辑：如果司机日收益 > 日基线收益，则排序值 = 0，否则 = 日收益/日基线收益
                    if (driverDayRevenue > baselineDay) {
                        return 0.0;
                    } else {
                        return driverDayRevenue / baselineDay;
                    }
                    
                } catch (Exception e) {
                    return 0.0;
                }
            })
            .collect(Collectors.toList());

        // 使用正向归一化 (收益均衡度越高越好)
        List<Double> normalizedScores = normalizeScores(rawScores, EnhancedNormalizer.MIN_MAX);

        for (int i = 0; i < models.size(); i++) {
            String driverId = models.get(i).getModel().getDriver().getDriverId().toString();
            scores.put(driverId, normalizedScores.get(i));
        }

        return scores;
    }

    /**
     * 计算区域调度优化分数
     */
    private Map<String, Double> calculateRegionalDispatch(List<SortModel> models, SortContext context) {
        Map<String, Double> scores = new HashMap<>();
        List<Double> rawScores = models.stream()
            .map(model -> {
                try {
                    // 这里可以根据区域供需平衡、司机分布等计算区域调度优化分数
                    // 暂时使用简单逻辑
                    return 0.6; // 默认60%优化度
                } catch (Exception e) {
                    return 0.6;
                }
            })
            .collect(Collectors.toList());

        // 使用正向归一化
        List<Double> normalizedScores = normalizeScores(rawScores, EnhancedNormalizer.MIN_MAX);

        for (int i = 0; i < models.size(); i++) {
            String driverId = models.get(i).getModel().getDriver().getDriverId().toString();
            scores.put(driverId, normalizedScores.get(i));
        }

        return scores;
    }

    /**
     * 计算高峰时段策略分数
     */
    private Map<String, Double> calculatePeakStrategy(List<SortModel> models, SortContext context) {
        Map<String, Double> scores = new HashMap<>();
        List<Double> rawScores = models.stream()
            .map(model -> {
                try {
                    // 根据当前时段和司机在高峰时段的表现计算分数
                    int currentHour = java.time.LocalDateTime.now().getHour();
                    boolean isPeakHour = isPeakHour(currentHour);
                    
                    if (isPeakHour) {
                        // 高峰时段，优先考虑高峰表现好的司机
                        return 0.8; // 默认80%高峰适应度
                    } else {
                        // 非高峰时段，正常评分
                        return 0.6; // 默认60%适应度
                    }
                } catch (Exception e) {
                    return 0.6;
                }
            })
            .collect(Collectors.toList());

        // 使用正向归一化
        List<Double> normalizedScores = normalizeScores(rawScores, EnhancedNormalizer.MIN_MAX);

        for (int i = 0; i < models.size(); i++) {
            String driverId = models.get(i).getModel().getDriver().getDriverId().toString();
            scores.put(driverId, normalizedScores.get(i));
        }

        return scores;
    }

    /**
     * 计算未来接单能力分数 (对应原F11特征)
     */
    private Map<String, Double> calculateFutureOrderCapacity(List<SortModel> models, SortContext context) {
        Map<String, Double> scores = new HashMap<>();
        List<Double> rawScores = models.stream()
            .map(model -> {
                try {
                    DriverVO driver = model.getModel().getDriver();
                    DspOrderVO order = model.getModel().getOrder();
                    
                    // 实现原F11逻辑：计算司机未来接单能力
                    // 将待派订单插入到司机接单列表，计算每段空驶能插入的订单数量
                    List<DspOrderVO> orderList = context.getOrderList(driver);
                    
                    double futureCapacity = 0.0;
                    
                    // 简化计算：基于司机工作时间和当前订单安排计算未来接单能力
                    if (driver.getWorkTimes() != null && !driver.getWorkTimes().isEmpty()) {
                        // 计算工作时间内的空闲时段
                        // 这里需要复杂的时间计算逻辑，暂时简化
                        futureCapacity = calculateSimplifiedFutureCapacity(driver, orderList);
                    }
                    
                    return futureCapacity;
                    
                } catch (Exception e) {
                    return 0.0;
                }
            })
            .collect(Collectors.toList());

        // 使用正向归一化 (未来接单能力越强越好)
        List<Double> normalizedScores = normalizeScores(rawScores, EnhancedNormalizer.MIN_MAX);

        for (int i = 0; i < models.size(); i++) {
            String driverId = models.get(i).getModel().getDriver().getDriverId().toString();
            scores.put(driverId, normalizedScores.get(i));
        }

        return scores;
    }

    /**
     * 计算司机分层分数 (对应原F19特征)
     */
    private Map<String, Double> calculateDriverTier(List<SortModel> models, SortContext context) {
        Map<String, Double> scores = new HashMap<>();
        List<Double> rawScores = models.stream()
            .map(model -> {
                try {
                    DriverVO driver = model.getModel().getDriver();
                    DspOrderVO order = model.getModel().getOrder();
                    
                    // 实现原F19的复杂分层逻辑
                    double tierScore = calculateDriverTierScore(driver, order, context);
                    return tierScore;
                    
                } catch (Exception e) {
                    return 1.0; // 默认最低分层
                }
            })
            .collect(Collectors.toList());

        // 原F19使用无需归一，这里保持原样
        List<Double> normalizedScores = normalizeScores(rawScores, EnhancedNormalizer.NONE);

        for (int i = 0; i < models.size(); i++) {
            String driverId = models.get(i).getModel().getDriver().getDriverId().toString();
            scores.put(driverId, normalizedScores.get(i));
        }

        return scores;
    }

    /**
     * 计算司机分层分数 (实现原F19逻辑)
     */
    private double calculateDriverTierScore(DriverVO driver, DspOrderVO order, SortContext context) {
        double tierScore = 10.0; // 初始分数
        
        try {
            // 1. 检查司机分是否在前200
            DriverPointsVO driverPoints = context.getDriverPoints(driver.getDriverId()).orElse(null);
            List<DriverPointsVO> topDrivers = context.getTopDriverPoints(200);
            
            boolean isInTop200 = false;
            if (driverPoints != null && !topDrivers.isEmpty()) {
                isInTop200 = topDrivers.stream()
                    .anyMatch(top -> top.getDriverId().equals(driver.getDriverId()));
            }
            
            if (!isInTop200) {
                return 1.0; // 不在前200，直接返回1
            }
            
            // 2. 检查司机缺勤天数
            DriverAttendanceVO attendance = context.getDriverAttendance(driver.getDriverId()).orElse(null);
            if (attendance != null && attendance.getAbsenceDays() != null && attendance.getAbsenceDays() > ABSENCE_DAY_THRESHOLD) {
                return 1.0; // 缺勤天数>15，直接返回1
            }
            
            // 3. 检查司机当日总订单流水
            DriverMileageProfitVO todayProfit = context.getTodayDriverMileageProfit(driver.getDriverId());
            Integer highProfitLine = context.getHighProfitLineDay(order.getCityId().longValue(), driver.getCar().getCarTypeId());
            
            if (todayProfit != null && highProfitLine != null && todayProfit.getProfit() != null) {
                if (todayProfit.getProfit() > highProfitLine) {
                    return 1.0; // 超过日收益高基线，直接返回1
                }
            }
            
            // 4. 检查预估订单收益+虚拟收入
            DriverMileageProfitVO expectProfit = context.getExpectDriverMileageProfit(driver.getDriverId());
            Integer baselineDay = context.getProfitBaselineDay(order.getCityId(), driver.getCar().getCarTypeId());
            
            if (expectProfit != null && baselineDay != null && expectProfit.getProfit() != null) {
                if (expectProfit.getProfit() < baselineDay) {
                    tierScore *= 10.0; // 低于基线，分数乘以10
                } else {
                    tierScore *= 1.0; // 高于基线，分数乘以1
                }
            }
            
            return tierScore;
            
        } catch (Exception e) {
            logger.error("Failed to calculate driver tier score", e);
            return 1.0;
        }
    }

    /**
     * 简化的未来接单能力计算
     */
    private double calculateSimplifiedFutureCapacity(DriverVO driver, List<DspOrderVO> orderList) {
        // 这里应该实现复杂的时间计算逻辑
        // 暂时返回基于订单数量的简化计算
        if (orderList == null || orderList.isEmpty()) {
            return 5.0; // 没有订单时，假设有较高的接单能力
        }
        
        // 基于当前订单数量反向计算剩余接单能力
        return Math.max(0.0, 5.0 - orderList.size());
    }

    /**
     * 判断是否是高峰时段
     */
    private boolean isPeakHour(int hour) {
        return (hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19);
    }

    /**
     * 获取子项权重配置
     */
    private Map<String, Double> getSubItemWeights() {
        Map<String, Double> weights = new HashMap<>();
        weights.put("REVENUE_BALANCE", config.getSubItemWeight("GLOBAL_EFFICIENCY", "REVENUE_BALANCE"));
        weights.put("REGIONAL_DISPATCH", config.getSubItemWeight("GLOBAL_EFFICIENCY", "REGIONAL_DISPATCH"));
        weights.put("PEAK_STRATEGY", config.getSubItemWeight("GLOBAL_EFFICIENCY", "PEAK_STRATEGY"));
        weights.put("FUTURE_ORDER_CAPACITY", config.getSubItemWeight("GLOBAL_EFFICIENCY", "FUTURE_ORDER_CAPACITY"));
        weights.put("DRIVER_TIER", config.getSubItemWeight("GLOBAL_EFFICIENCY", "DRIVER_TIER"));
        return weights;
    }

    /**
     * 聚合子项分数为类别分数
     */
    private Map<String, Double> aggregateSubItemScores(Map<String, Map<String, Double>> subItemScores, 
                                                      Map<String, Double> subItemWeights) {
        Map<String, Double> categoryScores = new HashMap<>();
        
        // 获取所有司机ID
        Set<String> driverIds = subItemScores.values().stream()
            .flatMap(map -> map.keySet().stream())
            .collect(Collectors.toSet());
        
        // 为每个司机计算类别分数
        for (String driverId : driverIds) {
            double weightedSum = 0.0;
            double totalWeight = 0.0;
            
            for (Map.Entry<String, Map<String, Double>> entry : subItemScores.entrySet()) {
                String subItemId = entry.getKey();
                Map<String, Double> driverScores = entry.getValue();
                
                Double driverScore = driverScores.get(driverId);
                Double weight = subItemWeights.get(subItemId);
                
                if (driverScore != null && weight != null) {
                    weightedSum += driverScore * weight;
                    totalWeight += weight;
                }
            }
            
            // 计算加权平均分数，并映射到[0-100]区间
            double categoryScore = totalWeight > 0 ? (weightedSum / totalWeight) * 100.0 : 0.0;
            categoryScores.put(driverId, Math.max(0.0, Math.min(100.0, categoryScore)));
        }
        
        return categoryScores;
    }

    /**
     * 归一化分数
     */
    private List<Double> normalizeScores(List<Double> rawScores, EnhancedNormalizer normalizer) {
        if (rawScores == null || rawScores.isEmpty()) {
            return rawScores;
        }
        
        try {
            NormalizationContext context = NormalizationContext.fromValues(rawScores);
            return rawScores.stream()
                .map(score -> normalizer.normalize(score, context))
                .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to normalize scores", e);
            return rawScores;
        }
    }

    /**
     * 创建降级分数
     */
    private Map<String, Double> createFallbackScores(List<SortModel> models) {
        Map<String, Double> fallbackScores = new HashMap<>();
        for (SortModel model : models) {
            String driverId = model.getModel().getDriver().getDriverId().toString();
            fallbackScores.put(driverId, 60.0); // 默认中等分数
        }
        return fallbackScores;
    }

    @Override
    public String getCategoryName() {
        return "GLOBAL_EFFICIENCY";
    }

    @Override
    public List<String> getSubItems() {
        return subItems;
    }

    @Override
    public double getSubItemWeight(String subItemId) {
        return config.getSubItemWeight("GLOBAL_EFFICIENCY", subItemId);
    }
}
