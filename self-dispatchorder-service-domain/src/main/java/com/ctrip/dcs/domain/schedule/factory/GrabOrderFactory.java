package com.ctrip.dcs.domain.schedule.factory;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface GrabOrderFactory {

    /**
     * 创建待抢订单
     * @param duid
     * @param subSku
     * @param drivers
     * @return
     */
    List<GrabOrderDO> create(DspOrderVO dspOrder, DuidVO duid, SubSkuVO subSku, List<DriverVO> drivers);

    List<GrabOrderDO> create(String dspOrderId, String userOrderId, Date estimatedUseTime, Integer premiumOrderFlag, String duid, SubSkuVO subSku, List<DriverVO> drivers);

    List<GrabOrderDO> create(String dspOrderId, String userOrderId, Date estimatedUseTime, Integer premiumOrderFlag, String duid, SubSkuVO subSku, Set<Long> driverIds);

}
