package com.ctrip.dcs.domain.schedule.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class DspOrderRewardStrategyDO {

    /**
     * ID
     */
    private Long id;

    /**
     * 派发单id
     */
    private String dspOrderId;

    /**
     * 用户订单id
     */
    private String userOrderId;

    /**
     * 调度id
     */
    private Long scheduleId;

    /**
     * 状态：0-待生效，1-生效，2-删除
     */
    private Integer dspRewardStatus;

    /**
     * 加价奖励类型。1-系统播报抢单加价；2-VBK人工加价；3-VBK系统触发加价
     */
    private Integer dspRewardType;

    /**
     * 预估司机结算价
     */
    private BigDecimal preDriverSettleAmount;

    /**
     * 预估司机结算价币种
     */
    private String preDriverSettleCurrency;

    /**
     * 加价奖励金额
     */
    private BigDecimal dspRewardAmount;

    /**
     * 加价奖励币种
     */
    private String dspRewardCurrency;

    /**
     * 预估总金额
     */
    private BigDecimal preTotalAmount;

    /**
     * 预估总金额币种
     */
    private String preTotalCurrency;

    /**
     * 奖励生效时间-当地
     */
    private Date dspRewardTimeLocal;

    /**
     * 奖励生效时间-北京
     */
    private Date dspRewardTimeBj;

    /**
     * 创建时间
     */
    private Date datachangeCreatetime;

    /**
     * 更新时间
     */
    private Date datachangeLasttime;

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDspOrderId() {
        return dspOrderId;
    }

    public void setDspOrderId(String dspOrderId) {
        this.dspOrderId = dspOrderId;
    }

    public String getUserOrderId() {
        return userOrderId;
    }

    public void setUserOrderId(String userOrderId) {
        this.userOrderId = userOrderId;
    }

    public Long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public Integer getDspRewardStatus() {
        return dspRewardStatus;
    }

    public void setDspRewardStatus(Integer dspRewardStatus) {
        this.dspRewardStatus = dspRewardStatus;
    }

    public Integer getDspRewardType() {
        return dspRewardType;
    }

    public void setDspRewardType(Integer dspRewardType) {
        this.dspRewardType = dspRewardType;
    }

    public BigDecimal getDspRewardAmount() {
        return dspRewardAmount;
    }

    public void setDspRewardAmount(BigDecimal dspRewardAmount) {
        this.dspRewardAmount = dspRewardAmount;
    }

    public String getDspRewardCurrency() {
        return dspRewardCurrency;
    }

    public void setDspRewardCurrency(String dspRewardCurrency) {
        this.dspRewardCurrency = dspRewardCurrency;
    }

    public Date getDspRewardTimeLocal() {
        return dspRewardTimeLocal;
    }

    public void setDspRewardTimeLocal(Date dspRewardTimeLocal) {
        this.dspRewardTimeLocal = dspRewardTimeLocal;
    }

    public Date getDspRewardTimeBj() {
        return dspRewardTimeBj;
    }

    public void setDspRewardTimeBj(Date dspRewardTimeBj) {
        this.dspRewardTimeBj = dspRewardTimeBj;
    }

    public Date getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Date datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Date getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Date datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public BigDecimal getPreDriverSettleAmount() {
        return preDriverSettleAmount;
    }

    public void setPreDriverSettleAmount(BigDecimal preDriverSettleAmount) {
        this.preDriverSettleAmount = preDriverSettleAmount;
    }

    public String getPreDriverSettleCurrency() {
        return preDriverSettleCurrency;
    }

    public void setPreDriverSettleCurrency(String preDriverSettleCurrency) {
        this.preDriverSettleCurrency = preDriverSettleCurrency;
    }

    public BigDecimal getPreTotalAmount() {
        return preTotalAmount;
    }

    public void setPreTotalAmount(BigDecimal preTotalAmount) {
        this.preTotalAmount = preTotalAmount;
    }

    public String getPreTotalCurrency() {
        return preTotalCurrency;
    }

    public void setPreTotalCurrency(String preTotalCurrency) {
        this.preTotalCurrency = preTotalCurrency;
    }
}
