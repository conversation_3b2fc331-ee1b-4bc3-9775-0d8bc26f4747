package com.ctrip.dcs.domain.schedule.value;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2023/3/17 14:37
 */
@Getter
@Setter
public class OrderDisLimitVO {
    private Integer bookDay;

    private Long drvId;

    private Long transportGroupId;

    private Integer transportGroupMode;

    private Integer drvCarTypeId;

    private Integer coopMode;


    private Integer cityId;

    private Integer topLevel;

    private String limitConf;

    private Integer limitDisLower;

    private Integer limitDisUpper;

    private String supplyOrderId;

    private String orderId;

    private Integer orderCarTypeId;

    private Double orderLen;

    private Date bookTime;

    private Integer dspType;

    private Integer hadTakenNum;

    private Integer takenLimit;


    public OrderDisLimitVO copyObject(){
        OrderDisLimitVO newOrderDisLimitModel = new OrderDisLimitVO();
        BeanUtils.copyProperties(this,newOrderDisLimitModel);
        return newOrderDisLimitModel;
    }
}
