package com.ctrip.dcs.domain.schedule.value;

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryDriverInfoCondition {
    /**
     * 供应子单号
     */
    private String dspOrderId;

    /**
     * 运力组id 可能为空
     */
    private Integer transportGroupId;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 司机名称-支持模糊
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 车牌号-可模糊搜索
     */
    private String carLicense;

    /**
     * 车型id
     */
    private Long carTypeId;

    /**
     * 二级品类，用来区分包车和非包车
     */
    private CategoryCodeEnum categoryCode;
    /**
     * 是否是司导灰度供应商
     */
    private Boolean driverGuideGraySupplier;

    /**
     * 是否是三期新流程
     */
    private Boolean newProcess;

    /**
     * 运力组id
     */
    private List<Long> transportGroupIdList;

}
