package com.ctrip.dcs.domain.orderPriority.service;

import com.ctrip.dcs.domain.common.service.OrderFeePriorityService;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DspOrderFeeQuantileVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderFeeDO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderFeeRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.orderPriority.entity.OrderFeeQuantileDO;
import com.ctrip.dcs.domain.orderPriority.repository.OrderPriorityRepository;
import com.ctrip.dcs.domain.schedule.check.value.OrderPriorityType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Component
public class OrderFeePriorityServiceImpl implements OrderFeePriorityService {
    private static final Logger logger = LoggerFactory.getLogger(OrderFeePriorityServiceImpl.class);

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    private DspOrderFeeRepository dspOrderFeeRepository;

    @Autowired
    private OrderPriorityRepository orderPriorityRepository;


    @Override
    public void dealOrderFeePriority(Double highPriority, Double mediumPriority){
        //查询前一天0点到今天0点，所有未完成的国内接送机订单
        //以车型+城市为维度分组
        try {
            List<DspOrderDO> dspOrderDOS = dspOrderRepository.queryYesterdayOrders();
            if (CollectionUtils.isEmpty(dspOrderDOS)) {
                logger.error("city_order_fee_quantile_task_id");
                return;
            }
            Table<Integer /*cityCode*/, Integer /*carType*/, Set<BigDecimal> /*driverOrderFee*/> table = HashBasedTable.create();
            for (DspOrderDO dspOrder : dspOrderDOS) {
                DspOrderFeeDO dspOrderFeeDO = dspOrderFeeRepository.find(dspOrder.getDspOrderId());
                if(dspOrderFeeDO == null){
                    continue;
                }
                Integer cityId =dspOrder.getCityId();
                Integer carTypeId = dspOrder.getVehicleGroupId();
                BigDecimal driverOrderFee = dspOrderFeeDO.getCostAmount();
                if (!table.contains(cityId, carTypeId)) {
                    table.put(cityId, carTypeId, Sets.newTreeSet(BigDecimal::compareTo));
                }
                Set<BigDecimal> drvOrderFee = table.get(cityId, carTypeId);
                if (drvOrderFee != null) {
                    drvOrderFee.add(driverOrderFee);
                }
            }
            Map<String /*cityId*/, String /*List<OrderFeeQuantileDTO>*/> data = Maps.newHashMap();
            for (Integer cityId : table.rowKeySet()) {
                Map<Integer, Set<BigDecimal>> map = table.rowMap().get(cityId);
                if (MapUtils.isEmpty(map)) {
                    continue;
                }
                List<OrderFeeQuantileDO> list = Lists.newArrayList();
                for (Integer carTypeId : map.keySet()) {
                    if (CollectionUtils.isEmpty(map.get(carTypeId))) {
                        continue;
                    }
                    List<BigDecimal> temp = Lists.newArrayList(map.get(carTypeId));
                    int highIndex = (int) (temp.size() * highPriority);
                    int mediumIndex = (int) (temp.size() * mediumPriority);
                    OrderFeeQuantileDO dto = new OrderFeeQuantileDO();
                    dto.setCityId(cityId);
                    dto.setCarTypeId(carTypeId);
                    dto.setHigh(temp.get(highIndex));
                    dto.setMedium(temp.get(mediumIndex));
                    list.add(dto);
                }
                data.put(cityId.toString(), JacksonSerializer.INSTANCE().serialize(list));
            }
            orderPriorityRepository.save(data);
        } catch (Exception e) {
            logger.error("city_order_fee_quantile_error",e);
            MetricsUtil.recordValue("orderQuantileByCityId");
        }
    }

    @Override
    public List<DspOrderFeeQuantileVO> queryOrderFeeQuantile(Integer cityId){
        List<OrderFeeQuantileDO> list = orderPriorityRepository.find(cityId);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream()
                .map(fee -> new DspOrderFeeQuantileVO(fee.getCityId(), fee.getCarTypeId(), fee.getHigh(), fee.getMedium()))
                .collect(Collectors.toList());
    }

    /**
     * 查询订单等级
     *
     * @return
     */
    @Override
    public OrderPriorityType queryOrderPriorityType(DspOrderVO order) {

        if(order == null){
            //默认是高等级
            return OrderPriorityType.HIGH;
        }
        List<OrderFeeQuantileDO> list = orderPriorityRepository.find(order.getCityId());
        if (CollectionUtils.isEmpty(list)) {
            // 兜底逻辑，防止下游接口异常，导致所有司机都接不了单
            return OrderPriorityType.HIGH;
        }
        for (OrderFeeQuantileDO dto : list) {
            if (Objects.equals(order.getCarTypeId(), dto.getCarTypeId())) {
                if (order.getCostAmount()!= null && order.getCostAmount().compareTo(dto.getHigh()) >= 0) {
                    return OrderPriorityType.HIGH;
                }
                if (order.getCostAmount()!= null && order.getCostAmount().compareTo(dto.getMedium())>= 0) {
                    return OrderPriorityType.MEDIUM;
                }
                return OrderPriorityType.LOW;
            }
        }
        // 兜底逻辑，若该城市没有订单车型等级，默认高等级
        return OrderPriorityType.HIGH;
    }
}
