package com.ctrip.dcs.domain.schedule.sort.feature;

/**
 * <AUTHOR>
 */
public enum FeatureItemId {

    /**
     * 司机分
     */
    F1("DRIVER_POINT_FEATURE"),

    /**
     * 司机分占比
     */
    F2("DRIVER_POINT_RATIO_FEATURE"),

    /**
     * 接驾时间成本
     */
    F4("DRIVER_TAKEN_TIME_COST_FEATURE"),

    /**
     * 接驾距离成本
     */
    F5("DRIVER_TAKEN_TIME_RANGE_FEATURE"),

    /**
     * 空驶
     */
    F6("DRIVER_EMPTY_DISTANCE_FEATURE"),

    /**
     * 订单时间间隔
     */
    F7("DRIVER_TIME_INTERVAL_FEATURE"),

    /**
     * 局部时间间隔
     */
    F9("DRIVER_LOCAL_TIME_INTERVAL_FEATURE"),

    /**
     * 局部空驶
     */
    F10("DRIVER_LOCAL_EMPTY_DISTANCE_FEATURE"),

    /**
     * 未来接单能力
     */
    F11("FUTURE_ORDER_NUM_EFFECT_FEATURE"),

    /**
     * 司机日收益
     */
    F13("DRIVER_DAY_PROFIT_FEATURE"),

    /**
     * 司机月收益
     */
    F12("DRIVER_MONTH_PROFIT_FEATURE"),

    /**
     * 里程价值
     */
    F14("ORDER_MILEAGE_VALUE_FEATURE"),

    /**
     * 增加的收益
     */
    F15("DRIVER_ADD_PROFIT_FEATURE"),

    /**
     * 增加的空驶
     */
    F16("DRIVER_ADD_EMPTY_DISTANCE_FEATURE"),

    /**
     * 供应商订单分流特征
     */
    F17("SUPPLIER_ORDER_DIVERSION_FEATURE"),

    /**
     * 司机分层
     */
    F18("GRADIENT_DRIVER_FEATURE"),

    /**
     * 司机分层新
     */
    F19("GRADIENT_DRIVER_NEW_FEATURE"),

    /**
     * 司机抢单时间排序
     */
    F20("DRIVER_GRAB_TIME_FEATURE"),

    /**
     * 司机空驶时长排序
     */
    F21("DRIVER_EMPTY_DRIVING_DURATION_FEATURE"),

    /**
     * 司机订单间隔时长排序
     */
    F22("DRIVER_ORDER_GAP_DURATION_FEATURE"),

    /**
     * 分层排序项，取对数
     */
    F23("GRADIENT_DRIVER_LOG_FEATURE"),

    /**
     * 用车时间和排序时间差
     */
    F24("BOOK_TO_RANK_DIFF_HOUR"),

    /**
     * 临近单距离耗时优先
     */
    F39("NEAR_ORDER_FEATURE"),

    /**
     * 1类2类3类司机分层排序
     */
    F40("DRIVER_CATEGORY_FEATURE"),

    /**
     * 司机订单衔接排序
     */
    F41("DRIVER_ORDER_CONNECTION_FEATURE"),

    /**
     * 高峰时段司机
     */
    F42("PEAK_HOUR_DRIVER_FEATURE"),
    ;

    private String value;

    FeatureItemId(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
