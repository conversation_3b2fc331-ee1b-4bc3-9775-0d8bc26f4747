package com.ctrip.dcs.domain.schedule.value;

import com.ctrip.dcs.domain.schedule.value.carconfig.ScheduleStrategyItemValueVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleStrategyVO {

    private Long strategyId;

    private List<ScheduleStrategyItemValueVO> items;
}
