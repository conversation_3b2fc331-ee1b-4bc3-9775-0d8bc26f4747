package com.ctrip.dcs.domain.schedule.repository;

import com.ctrip.dcs.domain.common.value.graborder.GrabOrderDTO;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface GrabOrderDetailRepository {

    void save(List<GrabOrderDO> orders);

    void delete(String dspOrderId, Long driverId);

    void deleteAll(String dspOrderId);

    void saveFullData(List<GrabOrderDTO> list);

    void deleteByDuidAndDriverId(String duid, Long driverId);

    void saveOrderId_DuidSetMapping(List<GrabOrderDO> orders);

    void saveDuid_DriverIdSetMapping(List<GrabOrderDO> orders);

    void cacheCheckResultForGrab(String dspOrderId, Set<Long> collect, Integer checkResultCacheConfigSeconds);

    List<String> getCachedCheckResultForGrab(String dspOrderId, Set<Long> collect);
}
