package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.DriverInServiceGateway;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DriverInServiceVisitor extends AbstractVisitor {

    private List<DriverVO> drivers;

    private DriverInServiceGateway driverInServiceGateway;

    public DriverInServiceVisitor(List<DriverVO> drivers) {
        Assert.notEmpty(drivers);
        this.drivers = drivers;
        this.driverInServiceGateway = getInstance(DriverInServiceGateway.class);
    }

    public DriverInServiceVisitor(List<DriverVO> drivers, DriverInServiceGateway driverInServiceGateway) {
        this.drivers = drivers;
        this.driverInServiceGateway = driverInServiceGateway;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, Boolean> map = context.getDriverInServiceMap();
        List<DriverVO> list = filter(drivers, map.keySet());
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> driverIds = list.stream().map(DriverVO::getDriverId).collect(Collectors.toList());
            Map<Long, Boolean> result = driverInServiceGateway.queryDriverInService(driverIds);
            map.putAll(result);
        }
    }
}
