package com.ctrip.dcs.domain.schedule.value;

public class BiStaticDriverInfo {
    /**
     * 司机ID
     */
    private Integer driver_id_last;
//    /**
//     * 完成单量
//     */
//    private Integer complete_cnt;
//    /**
//     * 缺陷单量
//     */
//    private Integer defect_cnt;
    /**
     * 月份
     */
    private String use_month_bj;
    /**
     * 是否是金牌司机 1表示是金牌司机
     */
    private Integer is_good_driver;

    public Integer getDriver_id_last() {
        return driver_id_last;
    }

    public void setDriver_id_last(Integer driver_id_last) {
        this.driver_id_last = driver_id_last;
    }

//    public Integer getComplete_cnt() {
//        return complete_cnt;
//    }
//
//    public void setComplete_cnt(Integer complete_cnt) {
//        this.complete_cnt = complete_cnt;
//    }
//
//    public Integer getDefect_cnt() {
//        return defect_cnt;
//    }
//
//    public void setDefect_cnt(Integer defect_cnt) {
//        this.defect_cnt = defect_cnt;
//    }

    public String getUse_month_bj() {
        return use_month_bj;
    }

    public void setUse_month_bj(String use_month_bj) {
        this.use_month_bj = use_month_bj;
    }

    public Integer getIs_good_driver() {
        return is_good_driver;
    }

    public void setIs_good_driver(Integer is_good_driver) {
        this.is_good_driver = is_good_driver;
    }
}
