package com.ctrip.dcs.domain.schedule.entity;

import com.ctrip.dcs.domain.common.enums.DispatcherGrabOrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.SupplierConfirmSceneEnum;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class DispatcherGrabOrderDO {

    private Long id;

    /**
     * 派发单id
     */
    private String dspOrderId;

    /**
     * 用户订单
     */
    private String userOrderId;

    /**
     * 供应商Id
     */
    private Long supplierId;

    /**
     * 运力组id
     */
    private Long transportGroupId;

    /**
     * 子产品id
     */
    private Integer subSku;

    /**
     * duid
     */
    private String duid;

    /**
     * 状态。0-待抢,1-抢单成功,2-拒单,3-订单取消
     */
    private DispatcherGrabOrderStatusEnum grabStatus;

    /**
     * 商家确认业务场景：0-抢单，1-急单， 2-原单修改-待商家同意
     */
    private Integer confirmScene;

    private String operatorUserId;

    private String operatorUserName;

    private String orderSource;

    private Date lastConfirmTime;

    private Date orderTime;

    private Integer modifyVersion;

    private String revokeReason;

    private String revokeReasonDesc;

    public DispatcherGrabOrderDO() {
    }

    @Builder
    public DispatcherGrabOrderDO(Long id, String dspOrderId, String userOrderId, Long supplierId, Long transportGroupId, Integer subSku, String duid, DispatcherGrabOrderStatusEnum grabStatus, Integer confirmScene, String operatorUserId, String operatorUserName, String orderSource, Date lastConfirmTime, Integer modifyVersion) {
        this.id = id;
        this.dspOrderId = dspOrderId;
        this.userOrderId = userOrderId;
        this.supplierId = supplierId;
        this.transportGroupId = transportGroupId;
        this.subSku = subSku;
        this.duid = duid;
        this.grabStatus = grabStatus;
        this.confirmScene = confirmScene;
        this.operatorUserId = operatorUserId;
        this.operatorUserName = operatorUserName;
        this.orderSource = orderSource;
        this.lastConfirmTime = lastConfirmTime;
        this.modifyVersion = modifyVersion;
    }

    public void id(Long id) {
        this.id = id;
    }

    public boolean isInit() {
        return DispatcherGrabOrderStatusEnum.INIT.equals(grabStatus);
    }

    public boolean isGrab() {
        return DispatcherGrabOrderStatusEnum.GRAB.equals(grabStatus);
    }

    public void submit(String operatorUserId, String operatorUserName) {
        this.operatorUserId = operatorUserId;
        this.operatorUserName = operatorUserName;
        grabStatus = DispatcherGrabOrderStatusEnum.GRAB;
    }

    public void refuse() {
        grabStatus = DispatcherGrabOrderStatusEnum.REFUSE;
    }

    public void cancel() {
        grabStatus = DispatcherGrabOrderStatusEnum.CANCEL;
    }

    public boolean isUrgent() {
        return Objects.equals(confirmScene, SupplierConfirmSceneEnum.URGENT.getScene());
    }
}
