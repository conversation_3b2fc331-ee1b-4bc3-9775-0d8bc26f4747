package com.ctrip.dcs.domain.schedule.visitor.impl;

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.service.HighLevelCheckService;
import com.ctrip.dcs.domain.common.value.CarVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.gateway.FirstClassDriverGateway;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.self.dispatchorder.interfaces.GoldDriverInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.HighLevelDriverInfo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

public class GoldDriverInfoVisitor extends AbstractVisitor {

    private List<CheckModel> checkModels;

    private HighLevelCheckService highLevelCheckService;

    public GoldDriverInfoVisitor(List<CheckModel> checkModels) {
        this.checkModels = checkModels;
        this.highLevelCheckService = getInstance(HighLevelCheckService.class);
    }

    public GoldDriverInfoVisitor(List<CheckModel> checkModels, HighLevelCheckService highLevelCheckService) {
        this.checkModels = checkModels;
        this.highLevelCheckService = highLevelCheckService;
    }

    @Override
    public void visit(CheckContext context) {
        if (CollectionUtils.isNotEmpty(checkModels)) {
            List<HighLevelDriverInfo> highLevelDriverList = Lists.newArrayList();
            for (CheckModel model : checkModels) {
                if (model.getModel() != null && model.getModel().getDriver() != null) {
                    HighLevelDriverInfo highLevelDriverInfo = new HighLevelDriverInfo();
                    DriverVO driver = model.getModel().getDriver();
                    highLevelDriverInfo.setDriverId(driver.getDriverId().intValue());
                    CarVO car = driver.getCar();
                    if (car != null) {
                        highLevelDriverInfo.setCarTypeId(car.getCarTypeId().intValue());
                    }
                    DspModelVO dspModelVO = model.getModel();
                    highLevelDriverInfo.setCityId(dspModelVO.getOrder().getCityId());
                    if (CategoryCodeEnum.isAirportOrStationOrPoint(dspModelVO.getOrder().getCategoryCode().getType())) {
                        highLevelDriverList.add(highLevelDriverInfo);
                    }
                }
            }
            List<GoldDriverInfo> goldDriverInfos = highLevelCheckService.checkHighLevelDriver(highLevelDriverList);
            Map<Integer, Boolean> highDriverMap = context.getHighDriverMap();
            if (CollectionUtils.isNotEmpty(goldDriverInfos)) {
                goldDriverInfos.forEach(goldDriverInfo -> highDriverMap.put(goldDriverInfo.getDriverId(), goldDriverInfo.isGoldDriver()));
            }
        }
    }

}
