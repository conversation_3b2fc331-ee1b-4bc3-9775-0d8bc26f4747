package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.gateway.QueryOrderTakenCostGateway;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.value.TakenCostVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DriverTakenCostsVisitor extends AbstractVisitor {

    private DspOrderVO dspOrder;

    private List<DriverVO> drivers;

    private QueryOrderTakenCostGateway queryOrderTakenCostGateway;

    public DriverTakenCostsVisitor(DspOrderVO dspOrder, List<DriverVO> drivers) {
        Assert.notNull(dspOrder);
        Assert.notEmpty(drivers);
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.queryOrderTakenCostGateway = getInstance(QueryOrderTakenCostGateway.class);
    }

    public DriverTakenCostsVisitor(DspOrderVO dspOrder, List<DriverVO> drivers, QueryOrderTakenCostGateway queryOrderTakenCostGateway) {
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.queryOrderTakenCostGateway = queryOrderTakenCostGateway;
    }

    @Override
    public void visit(SortContext context) {
        Map<Long, TakenCostVO> map = context.getDriverTakenCostMap();
        List<DriverVO> filter = filter(drivers, map.keySet());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        List<Integer> driverIds = filter.stream().map(DriverVO::getDriverId).map(Long::intValue).collect(Collectors.toList());
        List<TakenCostVO> list = queryOrderTakenCostGateway.queryOrderTakenCost(dspOrder.getDspOrderId(), driverIds);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (TakenCostVO cost : list) {
            map.put(cost.getDrvId().longValue(), cost);
        }
    }
}
