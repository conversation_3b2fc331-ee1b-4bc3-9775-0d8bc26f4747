package com.ctrip.dcs.domain.schedule.value;

import java.math.BigDecimal;

/**
 * 司机权益值对象
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @create 2023/9/22 14:41
 */
public class DriverRightsVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 权益定义ID
     */
    private Long rightsConfigid;

    /**
     * 权益类型 1福利金 2改派 3城市王者 4初级投诉豁免卡 5高级投诉豁免卡 6每日提现
     */
    private Integer rightsType;

    /**
     * 权益状态 0 已发放 1已使用 2已过期 3已作废
     */
    private Integer rightsStatus;

    /**
     * 权益名称
     */
    private String rightsName;

    /**
     * 权益简介
     */
    private String rightsDesc;

    /**
     * 当前时间，yyyy-MM
     */
    private String date;

    /**
     * 权益有效期开始时间
     */
    private String rightsStartTime;

    /**
     * 权益有效期借宿时间
     */
    private String rightsEndTime;

    /**
     * 发放时间
     */
    private String deliveryTime;

    /**
     * 使用限制 0无限次
     */
    private Integer useLimit;

    /**
     * 已使用次数
     */
    private Integer useCount;

    /**
     * 福利金上限
     */
    private BigDecimal welfareLimit;

    /**
     * 福利金使用金额
     */
    private BigDecimal welfareUsed;

    /**
     * 改派剩余次数（每周）
     */
    private Integer reassignUsedByWeek;

    public Long getId() {
        return id;
    }

    public Long getRightsConfigid() {
        return rightsConfigid;
    }

    public Integer getRightsType() {
        return rightsType;
    }

    public Integer getRightsStatus() {
        return rightsStatus;
    }

    public String getRightsName() {
        return rightsName;
    }

    public String getRightsDesc() {
        return rightsDesc;
    }

    public String getDate() {
        return date;
    }

    public String getRightsStartTime() {
        return rightsStartTime;
    }

    public String getRightsEndTime() {
        return rightsEndTime;
    }

    public String getDeliveryTime() {
        return deliveryTime;
    }

    public Integer getUseLimit() {
        return useLimit;
    }

    public Integer getUseCount() {
        return useCount;
    }

    public BigDecimal getWelfareLimit() {
        return welfareLimit;
    }

    public BigDecimal getWelfareUsed() {
        return welfareUsed;
    }

    public Integer getReassignUsedByWeek() {
        return reassignUsedByWeek;
    }

    public DriverRightsVO(Long id, Long rightsConfigid, Integer rightsType, Integer rightsStatus, String rightsName, String rightsDesc, String date, String rightsStartTime, String rightsEndTime, String deliveryTime, Integer useLimit, Integer useCount, BigDecimal welfareLimit, BigDecimal welfareUsed, Integer reassignUsedByWeek) {
        this.id = id;
        this.rightsConfigid = rightsConfigid;
        this.rightsType = rightsType;
        this.rightsStatus = rightsStatus;
        this.rightsName = rightsName;
        this.rightsDesc = rightsDesc;
        this.date = date;
        this.rightsStartTime = rightsStartTime;
        this.rightsEndTime = rightsEndTime;
        this.deliveryTime = deliveryTime;
        this.useLimit = useLimit;
        this.useCount = useCount;
        this.welfareLimit = welfareLimit;
        this.welfareUsed = welfareUsed;
        this.reassignUsedByWeek = reassignUsedByWeek;
    }

    /**
     * 查询本月剩余次数
     */
    public Integer getTimesForMonth() {
        if (getUseLimit() == null || getUseCount() == null) {
            return null;
        }
        if (getUseLimit() == 0) {
            return 0;
        }
        return getUseLimit() - getUseCount();
    }

}