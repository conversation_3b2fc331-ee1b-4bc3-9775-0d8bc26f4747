package com.ctrip.dcs.domain.schedule.service;

import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.entity.DspDrvOrderLimitTakenRecordDO;
import com.ctrip.dcs.domain.schedule.check.CheckCode;
import com.ctrip.dcs.domain.schedule.check.CheckItemRecord;
import com.ctrip.dcs.domain.schedule.sort.SortRecord;
import com.ctrip.dcs.domain.schedule.value.DeadHeadDisModelVO;
import com.ctrip.dcs.domain.schedule.value.SupplierOrderDiversionRecordVO;
import com.ctrip.dcs.domain.schedule.value.carconfig.HeadTailLimitValueVO;
import com.ctrip.dcs.domain.schedule.value.carconfig.NearPickUpTimeOrderConfigValueVO;
import com.ctrip.dcs.domain.schedule.value.carconfig.OrderMileageConfigValueVO;
import com.ctrip.dcs.domain.schedule.value.carconfig.SameCarTypeDspValueVO;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 派单上下文服务
 * <AUTHOR>
 */
public interface DspContextService {

    /**
     * 查询运力组
     * @param dspOrder
     * @return
     */
    List<TransportGroupVO> queryTransports(DspOrderVO dspOrder);

    List<TransportGroupVO> queryTransportList(List<Long> transportGroupIds);

    List<DriverVO> queryDriversByTransportGroupIds(Set<Long> transportGroupIds, Integer coopMode, ParentCategoryEnum parentCategoryEnum, Long supplierId);

    List<DriverVO> queryVBKGrabDrivers(DspOrderVO dspOrder);

    Integer getStandardProfitLineDay(Integer cityId, Long carTypeId);

    Integer getDeadHeadDisLimit(DeadHeadDisModelVO deadHeadDisModel, List<TransportGroupVO> transportGroupInfos);

    Integer getHighProfitLineDay(Long cityId, Long carTypeId);

    double getOrderConnectionDurationScore(Long cityId, Long durationMinutes);

    /**
     * 获取首尾单配置
     * @param cityId
     * @param carTypeId
     * @return
     */
    public HeadTailLimitValueVO queryHeadTailLimitValue(Long cityId, Long carTypeId);

    SameCarTypeDspValueVO querySameCarTypeDspValue(long cityId);

    /**
     * 查询司机接单数量阈值
     *
     * @param cityId                城市ID
     * @param vehicleModelCode      车型CODE
     * @param categoryCode          产线CODE
     * @param driverDistanceType    司机里程类型
     * @param orderDistanceType     订单里程类型
     */
    int queryDriverTakenDistanceOrderThreshold(Long cityId, Long vehicleModelCode, String categoryCode, Integer driverDistanceType, Integer orderDistanceType);

    /**
     * 获取订单价值配置值
     * @param dspOrder
     * @return
     */
    Double queryOrderMileageValueConfig(DspOrderVO dspOrder);

    /**
     * 检查记录
     * @param record
     */
    void record(CheckItemRecord record);

    /**
     * 排序记录
     * @param record
     */
    void record(SortRecord record);

    /**
     * 供应商分流记录
     * @param records
     */
    void record(DspOrderVO dspOrder, List<SupplierOrderDiversionRecordVO> records);

    void saveDeadHeadDisModel(DspOrderVO order, DriverVO driver, DeadHeadDisModelVO deadHeadDisModelVO);

    Integer getCheckResultCacheConfigSeconds(Integer cityId);

    void cacheCheckResultForGrab(String dspOrderId, Set<Long> collect, Integer checkResultCacheConfigSeconds);

    List<String> getCachedCheckResultForGrab(String dspOrderId, Set<Long> collect);

    /**
     * 查询订单延后派预确认检查通过运力信息
     *
     * @param dspOrder 订单信息
     * @return 预检查结果
     */
    OrderPreConfirmDTO queryOrderPreConfirmDTO(DspOrderVO dspOrder);

    void cachePreTakenSnapShot(Integer subSkuId, Long drvId, DspDrvOrderLimitTakenRecordDO orderDisLimitModel);


    /**
     * 获取订单价值配置
     * @param dspOrder
     * @return
     */
    OrderMileageConfigValueVO queryOrderMileageValueConfigValue(DspOrderVO dspOrder);

    /**
     * 获取临近用车订单判断时间配置
     * @param dspOrder
     * @return
     */
    NearPickUpTimeOrderConfigValueVO queryNearPickUpTimeOrderConfig(DspOrderVO dspOrder);

    /**
     * 校验运力组供应商是否合规
     * @param dspOrder
     * @param supplierIes
     * @return 合规的供应商id
     */
    List<Long> queryValidateTransportSupplier(DspOrderVO dspOrder, List<Long> supplierIes);
    /**
     * 从缓存中查询检查结果
     * @param key
     * @return
     */
    Map<String, CheckCode> queryCheckCode(List<String> key);

    /**
     * 缓存检查结果
     * @param expire 秒
     */
    void cacheCheckCode(Map<String, CheckCode> map, Integer expire);

    <K extends CarConfigKeyVO, V extends CarConfigValueVO> List<V> getCarConfig(String configName, List<K> key);

    Double queryPeakHourDriverFeatureConfigValue(Integer cityId);
}
