package com.ctrip.dcs.domain.schedule.value;


import java.io.Serializable;
import java.util.List;

public class DriverPushConfigVO implements Serializable {
    /**
     * 司机id
     */
    public Long driverId;
    /**
     *服务时间开始
     */
    public String serviceTimeFrom;
    /**
     *服务结束时间（可能跨天小于开始时间）
     */
    public String serviceTimeTo;
    /**
     * 订单类型：送机[1] 接机[2] 送站[4] 接站[5] 点对点[12]
     */
    public List<Integer> orderTypes;
    /**
     * 播报状态
     */
    public Boolean orderPushStatus;
    /**
     *距离司机公里数
     */
    public Integer drvOrderDistance;
    /**
     * 订单车型
     */
    public List<Integer> drvIntendVehicleTypes;

    public Long getDriverId() {
        return driverId;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public String getServiceTimeFrom() {
        return serviceTimeFrom;
    }

    public void setServiceTimeFrom(String serviceTimeFrom) {
        this.serviceTimeFrom = serviceTimeFrom;
    }

    public String getServiceTimeTo() {
        return serviceTimeTo;
    }

    public void setServiceTimeTo(String serviceTimeTo) {
        this.serviceTimeTo = serviceTimeTo;
    }

    public List<Integer> getOrderTypes() {
        return orderTypes;
    }

    public void setOrderTypes(List<Integer> orderTypes) {
        this.orderTypes = orderTypes;
    }

    public Boolean getOrderPushStatus() {
        return orderPushStatus;
    }

    public void setOrderPushStatus(Boolean orderPushStatus) {
        this.orderPushStatus = orderPushStatus;
    }

    public Integer getDrvOrderDistance() {
        return drvOrderDistance;
    }

    public void setDrvOrderDistance(Integer drvOrderDistance) {
        this.drvOrderDistance = drvOrderDistance;
    }

    public List<Integer> getDrvIntendVehicleTypes() {
        return drvIntendVehicleTypes;
    }

    public void setDrvIntendVehicleTypes(List<Integer> drvIntendVehicleTypes) {
        this.drvIntendVehicleTypes = drvIntendVehicleTypes;
    }
}
