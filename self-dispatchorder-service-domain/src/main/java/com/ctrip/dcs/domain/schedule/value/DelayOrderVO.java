package com.ctrip.dcs.domain.schedule.value;

import com.ctrip.dcs.domain.common.enums.YesOrNo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public final class DelayOrderVO {

    private String dspOrderId;

    private Integer isDelay;

    private Integer isVirtualDsp;

    private String executeTime;

    private String executeTimeDeadline;

    private DelayOrderVO() {
    }

    public static DelayOrderVO of(String dspOrderId) {
        DelayOrderVO delayOrder = new DelayOrderVO();
        delayOrder.dspOrderId = dspOrderId;
        delayOrder.isDelay = YesOrNo.NO.getCode();
        delayOrder.isVirtualDsp = YesOrNo.NO.getCode();
        return delayOrder;
    }

    public boolean isDelay() {
        return Objects.equals(YesOrNo.YES.getCode(), isDelay);
    }

    public boolean isVirtualDsp() {
        return Objects.equals(YesOrNo.YES.getCode(), isVirtualDsp);
    }
}
