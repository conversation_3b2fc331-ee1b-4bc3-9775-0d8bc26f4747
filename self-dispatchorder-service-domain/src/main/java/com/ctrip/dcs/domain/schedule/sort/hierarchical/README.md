# 分层排序系统 (Hierarchical Sorting System)

## 概述

分层排序系统是对现有司机打分排序系统的全面升级，采用分层分类的方式对司机进行评分和排序。系统将原有的单一线性加权模式升级为4大类别的分层评分模式，提供更科学、更灵活的司机排序方案。

## 系统架构

### 核心组件

1. **HierarchicalSorter** - 主排序器
   - 协调整个排序流程
   - 处理降级和异常情况
   - 提供统一的排序接口

2. **CategoryManager** - 类别管理器
   - 管理4大类别的特征计算
   - 协调子项分数聚合
   - 处理归一化逻辑

3. **GradeCalculator** - 等级计算器
   - 将分数转换为A/B/C/D等级
   - 计算加权GPA
   - 支持多种聚合策略

4. **HierarchicalSortConfig** - 配置管理器
   - 管理权重配置
   - 支持区域和时段差异化
   - 提供配置热更新

5. **SortingMonitor** - 监控器
   - 性能监控和告警
   - 错误率统计
   - 系统健康度评估

## 4大类别体系

### 1. 时空效率类 (TIME_SPACE_EFFICIENCY) - 权重45%

**目标**: 优化时间和空间资源利用效率

**子项**:
- **ETA** (25%) - 预估到达时间，对应原F4接驾时间成本
- **EMPTY_DISTANCE** (20%) - 空驶距离，对应原F6空驶距离
- **LOCAL_TIME_INTERVAL** (25%) - 局部时间间隔，对应原F9
- **LOCAL_EMPTY_DISTANCE** (20%) - 局部空驶距离，对应原F10
- **PICKUP_COST** (10%) - 接驾成本，对应原F5接驾距离成本

**归一化方式**: 
- ETA、空驶距离、接驾成本使用逆向归一化 (越小越好)
- 时间间隔使用正向归一化 (间隔优化值越大越好)

### 2. 服务质量类 (SERVICE_QUALITY) - 权重25%

**目标**: 保障服务质量和用户体验

**子项**:
- **DRIVER_RATING** (20%) - 司机评分
- **COMPLAINT_RATE** (15%) - 投诉率 (逆向)
- **COMPLETION_RATE** (15%) - 完单率
- **DRIVER_POINTS_RATIO** (35%) - 司机分占比，对应原F2
- **ACTIVITY_LEVEL** (15%) - 活跃度

**归一化方式**:
- 司机分占比使用对数归一化 (长尾分布)
- 投诉率使用逆向归一化
- 其他使用正向归一化

### 3. 订单匹配度类 (ORDER_MATCHING) - 权重20%

**目标**: 提高订单与司机的匹配精度

**子项**:
- **VEHICLE_MATCH** (30%) - 车型匹配度
- **ROUTE_COMPATIBILITY** (25%) - 路线顺路性
- **DRIVER_PREFERENCE** (25%) - 司机偏好匹配
- **ORDER_TYPE_MATCH** (20%) - 订单类型匹配

**归一化方式**: 全部使用正向归一化

### 4. 全局效率类 (GLOBAL_EFFICIENCY) - 权重10%

**目标**: 优化全局资源配置和长期效率

**子项**:
- **REVENUE_BALANCE** (30%) - 收益均衡，对应原F13司机日收益
- **REGIONAL_DISPATCH** (15%) - 区域调度优化
- **PEAK_STRATEGY** (15%) - 高峰时段策略
- **FUTURE_ORDER_CAPACITY** (20%) - 未来接单能力，对应原F11
- **DRIVER_TIER** (20%) - 司机分层，对应原F19

**归一化方式**:
- 收益均衡使用Z-score标准化
- 司机分层保持原有逻辑 (无需归一)
- 其他使用正向归一化

## 等级评分体系

### 等级定义
- **A级** (≥85分): 优秀 - GPA值4
- **B级** (70-84分): 良好 - GPA值3  
- **C级** (50-69分): 一般 - GPA值2
- **D级** (<50分): 较差 - GPA值1

### 总体等级计算策略

1. **加权平均策略** (默认)
   ```
   GPA = Σ(等级分 × 权重)
   总体等级 = 根据GPA映射到A/B/C/D
   ```

2. **短板效应策略**
   ```
   总体等级 = min(各类别等级)
   ```

### 排序逻辑

1. **首先按总体等级排序** (A > B > C > D)
2. **等级相同时按加权GPA排序**
3. **GPA相同时按类别优先级排序**:
   - 时空效率类 (最高优先级)
   - 服务质量类
   - 订单匹配度类
   - 全局效率类
4. **最后按子项细分分数排序**

## 归一化方法

系统提供多种归一化方法以适应不同类型的指标:

### 1. Min-Max归一化
```
x' = (x - min) / (max - min)
```
适用于有明确边界的指标

### 2. Z-score标准化
```
x' = (x - μ) / σ
```
适用于正态分布的指标

### 3. 对数归一化
```
x' = log(x) / log(max)
```
适用于长尾分布的指标

### 4. Sigmoid映射
```
x' = 1 / (1 + e^(-k*(x-c)))
```
适用于需要平滑过渡的指标

### 5. 排名归一化
```
x' = rank(x) / N
```
适用于相对排序重要的指标

## 配置管理

### 基础配置

```properties
# 启用分层排序
hierarchical.sort.enabled=true

# 类别权重配置
hierarchical.sort.category.weight.time_space_efficiency=0.45
hierarchical.sort.category.weight.service_quality=0.25
hierarchical.sort.category.weight.order_matching=0.20
hierarchical.sort.category.weight.global_efficiency=0.10

# 总体等级策略
hierarchical.sort.strategy.overall_grade=WEIGHTED_AVERAGE

# 降级阈值
hierarchical.sort.fallback_threshold=0.1
```

### 区域差异化配置

```properties
# 城市特定权重 (以北京为例)
hierarchical.sort.category.weight.city.1.time_space_efficiency=0.50
hierarchical.sort.category.weight.city.1.service_quality=0.30

# 时段特定权重
hierarchical.sort.category.weight.time.morning_peak.time_space_efficiency=0.55
hierarchical.sort.category.weight.time.evening_peak.global_efficiency=0.15
```

### 子项权重配置

```properties
# 时空效率类子项权重
hierarchical.sort.subitem.weight.time_space_efficiency.eta=0.25
hierarchical.sort.subitem.weight.time_space_efficiency.empty_distance=0.20
hierarchical.sort.subitem.weight.time_space_efficiency.local_time_interval=0.25
hierarchical.sort.subitem.weight.time_space_efficiency.local_empty_distance=0.20
hierarchical.sort.subitem.weight.time_space_efficiency.pickup_cost=0.10
```

## 监控和告警

### 性能指标
- 排序耗时
- 吞吐量 (每秒处理司机数)
- 成功率
- 错误率
- 降级率

### 告警阈值
- 错误率 > 10% 触发告警
- 降级率 > 5% 触发告警
- 平均耗时 > 1秒 影响健康度评分

### 健康度评估
```
健康度 = 100 - 错误率×50 - 降级率×30 - 性能影响
```

## 使用示例

### 基本使用

```java
// 创建工厂
HierarchicalSortFactory factory = new HierarchicalSortFactory(configService);

// 获取排序器
HierarchicalSorter sorter = factory.getHierarchicalSorter();

// 执行排序
List<SortModel> sortedModels = sorter.sort(models, context);

// 查看结果
for (SortModel model : sortedModels) {
    HierarchicalScore score = model.getHierarchicalScore();
    System.out.println("司机: " + model.getModel().getDriver().getDriverId() + 
                      ", 等级: " + score.getOverallGrade() + 
                      ", GPA: " + score.getWeightedGPA());
}
```

### 监控使用

```java
// 获取监控统计
Map<String, Object> stats = factory.getMonitoringStats();
System.out.println("成功率: " + stats.get("success_rate"));
System.out.println("健康度: " + factory.getSystemHealthScore());

// 执行诊断
Map<String, Object> diagnostics = factory.performDiagnostics();
```

## 降级策略

### 降级触发条件
1. 新系统出现异常
2. 错误率超过阈值
3. 性能严重下降

### 降级方案
1. **简化排序**: 只使用部分特征进行排序
2. **原始排序**: 回退到原有的线性加权排序
3. **基础排序**: 仅按司机分排序

### 降级恢复
- 自动监控系统状态
- 错误率下降后自动恢复
- 支持手动强制恢复

## 与原系统对比

| 特性 | 原系统 | 新系统 |
|------|--------|--------|
| 排序方式 | 线性加权求和 | 分层等级排序 |
| 特征数量 | 7个 (F2,F9,F10,F11,F13,F14,F19) | 17个子项，4大类别 |
| 归一化 | 4种基础方法 | 8种增强方法 |
| 配置灵活性 | 固定权重 | 区域时段差异化 |
| 监控能力 | 基础监控 | 全面监控告警 |
| 降级能力 | 无 | 多级降级策略 |

## 迁移指南

### 现有特征映射

| 原特征 | 新类别 | 新子项 | 说明 |
|--------|--------|--------|------|
| F2 司机分占比 | 服务质量类 | DRIVER_POINTS_RATIO | 保持原逻辑 |
| F9 局部时间间隔 | 时空效率类 | LOCAL_TIME_INTERVAL | 保持原逻辑 |
| F10 局部空驶距离 | 时空效率类 | LOCAL_EMPTY_DISTANCE | 保持原逻辑 |
| F11 未来接单能力 | 全局效率类 | FUTURE_ORDER_CAPACITY | 保持原逻辑 |
| F13 司机日收益 | 全局效率类 | REVENUE_BALANCE | 保持原逻辑 |
| F14 订单里程价值 | 时空效率类 | 多个子项组合 | 拆分为多个子项 |
| F19 司机分层 | 全局效率类 | DRIVER_TIER | 保持原逻辑 |

### 权重调整建议

基于原系统权重分析，建议的权重分配:
- 时空效率类: 45% (原F9,F10,F14权重较高)
- 服务质量类: 25% (原F2权重最高)
- 订单匹配度类: 20% (新增类别)
- 全局效率类: 10% (原F11,F13,F19权重较低)

## 最佳实践

### 配置建议
1. 根据业务场景调整类别权重
2. 高峰时段提高时空效率类权重
3. 服务敏感场景提高服务质量类权重
4. 定期评估和调整权重配置

### 监控建议
1. 设置合理的告警阈值
2. 定期查看健康度评分
3. 关注降级频率和原因
4. 监控各类别分数分布

### 性能优化
1. 合理设置批处理大小
2. 避免过于复杂的归一化计算
3. 缓存常用的配置和统计数据
4. 定期清理监控计数器

## 故障排查

### 常见问题
1. **配置权重总和不为1**: 检查配置文件，确保权重总和为1.0
2. **排序结果异常**: 检查各类别分数计算逻辑
3. **性能下降**: 检查归一化方法选择和数据量
4. **频繁降级**: 检查系统资源和异常日志

### 调试工具
1. 使用诊断接口查看系统状态
2. 查看监控统计识别问题
3. 启用详细日志跟踪计算过程
4. 使用测试用例验证逻辑正确性
