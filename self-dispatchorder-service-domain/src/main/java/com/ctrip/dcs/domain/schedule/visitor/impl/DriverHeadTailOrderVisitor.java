package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.service.HeadTailOrderService;
import com.ctrip.dcs.domain.common.value.DriverHeadTailOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DriverHeadTailOrderVisitor extends AbstractVisitor {

    private DspOrderVO dspOrder;

    private List<DriverVO> drivers;

    private HeadTailOrderService headTailOrderService;

    public DriverHeadTailOrderVisitor(DspOrderVO dspOrder, List<DriverVO> drivers) {
        Assert.notNull(dspOrder);
        Assert.notEmpty(drivers);
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.headTailOrderService = getInstance("headTailOrderServiceImpl", HeadTailOrderService.class);
    }

    public DriverHeadTailOrderVisitor(DspOrderVO dspOrder, List<DriverVO> drivers, String headTailOrderService) {
        Assert.notNull(dspOrder);
        Assert.notEmpty(drivers);
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.headTailOrderService = getInstance(headTailOrderService, HeadTailOrderService.class);
    }

    public DriverHeadTailOrderVisitor(DspOrderVO dspOrder, List<DriverVO> drivers, HeadTailOrderService headTailOrderService) {
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.headTailOrderService = headTailOrderService;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, DriverHeadTailOrderVO> orderMap = context.getDriverHeadTailOrderVOMap();
        List<DriverVO> filter = filter(drivers, orderMap.keySet());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        Map<Long, DriverHeadTailOrderVO> map = headTailOrderService.findHeadTailOrder(filter, dspOrder);
        orderMap.putAll(map);
    }
}
