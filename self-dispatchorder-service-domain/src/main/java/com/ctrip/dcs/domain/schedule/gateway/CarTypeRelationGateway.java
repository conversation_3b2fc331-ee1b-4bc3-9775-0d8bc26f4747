package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.schedule.value.CarTypeLevelRelationsVO;

public interface CarTypeRelationGateway {
    /**
     * 通过订单车型ID查询车型等级关系
     * @param carTypeId 订单车型
     * @return
     */
    CarTypeLevelRelationsVO queryCarTypeLevelRelations(Integer carTypeId);


    /**
     * 通过司机车型ID查询车型等级关系
     * @param drvCarTypeId 司机车型ID
     * @return
     */
    CarTypeLevelRelationsVO queryCarTypeLevelRelationsByDrvCarType(Integer drvCarTypeId);
}
