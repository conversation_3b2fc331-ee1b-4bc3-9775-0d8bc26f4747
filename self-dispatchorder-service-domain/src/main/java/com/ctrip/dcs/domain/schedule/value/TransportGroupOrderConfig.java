package com.ctrip.dcs.domain.schedule.value;

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.FixedLocationType;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.base.Splitter;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/2 16:01:17
 */
@Getter
@Setter
@NoArgsConstructor
public class TransportGroupOrderConfig {
    /**
     * 固定点位类型
     */
    private FixedLocationType fixedLocationType;

    /**
     * 固定点位编码
     */
    private String locationCode;

    /**
     * 配置是否有效
     */
    private Boolean active;

    /**
     * 不会出现跨天配置
     * 时段配置[{"time":"08:00-12:00","orderCount":20}]
     *   time：时段
     *   orderCount：订单上限
     */
    private List<OrderLimitByTime> configItems;

    public OrderLimitByTime match(String categoryCode, String locationCode, Date bookTime) {
        if (CollectionUtils.isEmpty(configItems)) {
            return null;
        }
        if (CategoryCodeEnum.isAirportOrStation(categoryCode) && !Objects.equals(locationCode, this.getLocationCode())) {
            return null;
        }
        for (OrderLimitByTime item : configItems) {
            if (item.match(bookTime)) {
                return item;
            }
        }
        return null;
    }

    public List<OrderLimitByTime> match(String categoryCode, String locationCode) {
        if (CollectionUtils.isEmpty(configItems)) {
            return null;
        }
        if (CategoryCodeEnum.isAirportOrStation(categoryCode) && !Objects.equals(locationCode, this.getLocationCode())) {
            return null;
        }
        return configItems;
    }

    /**
     * 时段单量配置
     */
    public static class OrderLimitByTime {
        private final Logger logger = LoggerFactory.getLogger(OrderLimitByTime.class);

        private final Splitter TIME_SPLITTER = Splitter.on('-').trimResults().omitEmptyStrings();

        /**
         * 时段
         * 不会出现跨天配置
         * 时段配置[{"time":"08:00-12:00","orderCount":20}]
         */
        private String time;

        /**
         * 订单上限
         */
        private Integer orderCount;

        public OrderLimitByTime() {
        }

        public OrderLimitByTime(String time, Integer orderCount) {
            this.time = time;
            this.orderCount = orderCount;
        }

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }

        public Integer getOrderCount() {
            return orderCount;
        }

        public void setOrderCount(Integer orderCount) {
            this.orderCount = orderCount;
        }

        public boolean match(Date bookTime) {
            if (StringUtils.isBlank(time)) {
                return false;
            }
            List<String> times = TIME_SPLITTER.splitToList(time);
            if (CollectionUtils.isEmpty(times) || times.size() != 2) {
                return false;
            }
            return match(times, bookTime);
        }

        public boolean match(List<String> times, Date bookTime) {
            try {
                String formatBookTime = DateUtil.formatDate(bookTime, DateUtil.TIME_FORMAT);
                String startTime = times.get(0);
                String endTime = times.get(1);
                int compStart = formatBookTime.compareTo(startTime);
                int compEnd = formatBookTime.compareTo(endTime);
                return startTime.compareTo(endTime) >= 0 ? (compStart >= 0 || compEnd <= 0) : (compStart >= 0 && compEnd <= 0);
            } catch (Exception e) {
                logger.error("OrderLimitByTime match error!", e);
            }
            return false;
        }

        public Date getBegin(Date bookTime) {
            String day = DateUtil.formatDate(bookTime, DateUtil.DATE_FORMAT);
            List<String> times = TIME_SPLITTER.splitToList(time);
            return DateUtil.parseDateStr2Date(day + " " + times.get(0));
        }

        public Date getEnd(Date bookTime) {
            String day = DateUtil.formatDate(bookTime, DateUtil.DATE_FORMAT);
            List<String> times = TIME_SPLITTER.splitToList(time);
            return DateUtil.parseDateStr2Date(day + " " + times.get(1));
        }
    }
}
