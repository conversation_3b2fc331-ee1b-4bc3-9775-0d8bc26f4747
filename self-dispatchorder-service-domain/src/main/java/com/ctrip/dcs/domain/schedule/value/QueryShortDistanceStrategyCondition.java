package com.ctrip.dcs.domain.schedule.value;

import com.ctrip.igt.PaginatorDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryShortDistanceStrategyCondition {
    /**
     * ID
     */
    private Long id;

    /**
     * 配置名称
     */
    private String code;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 品类编码
     */
    private List<String> categoryCodeList;

    /**
     * 渠道组id（1级-2级-3级）
     */
    private List<String> channelIdList;

    /**
     * 预订车组ID
     */
    private List<Long> vehicleGroupIdList;

    /**
     * 上下线状态：1上线，0下线
     */
    private Integer state;

    private PaginatorDTO paginator;
}
