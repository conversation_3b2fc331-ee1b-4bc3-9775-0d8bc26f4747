package com.ctrip.dcs.domain.schedule.service;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.command.DspSortCommand;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;

import java.util.List;

;

/**
 * <AUTHOR>
 */
public interface SortService {

    List<SortModel> sort(DspSortCommand command);

    /**
     * 排序
     * @param order
     * @param subSku
     * @param drivers
     * @return
     */
    List<DriverVO> sort(DspOrderVO order, SubSkuVO subSku, List<DriverVO> drivers);
}
