package com.ctrip.dcs.domain.schedule.exception;

import com.ctrip.dcs.domain.schedule.check.CheckCode;
import com.ctrip.igt.framework.common.exception.BizException;

public class CheckItemException extends BizException {

    private final CheckCode checkCode;

    public CheckItemException(CheckCode checkCode) {
        this.checkCode = checkCode;
    }

    public CheckCode getCheckCode() {
        return checkCode;
    }
}
