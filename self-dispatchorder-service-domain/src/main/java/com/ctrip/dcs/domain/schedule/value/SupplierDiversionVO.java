package com.ctrip.dcs.domain.schedule.value;

import com.ctrip.dcs.domain.common.enums.DiversionMatchEnum;
import com.ctrip.dcs.domain.common.enums.YesOrNo;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 */
public class SupplierDiversionVO {

    /**
     * 供应商
     */
    public Long supplierId;

    public Integer ratePlanId;

    /**
     * 分流配置
     */
    public BigDecimal diversionValue;
    /**
     * 订单数量
     */
    private Integer supplierOrderCount;

    private DiversionMatchEnum type;

    private Integer isConfigSupplier;

    private boolean isDispatchSupplier;

    public SupplierDiversionVO(Long supplierId, Integer ratePlanId,BigDecimal diversionValue, Integer supplierOrderCount, DiversionMatchEnum type, Boolean isConfigSupplier,Boolean isDispatchSupplier) {
        this.supplierId = supplierId;
        this.ratePlanId = ratePlanId;
        this.diversionValue = diversionValue;
        this.supplierOrderCount = supplierOrderCount;
        this.type = type;
        this.isConfigSupplier = isConfigSupplier ? YesOrNo.YES.getCode() : YesOrNo.NO.getCode();
        this.isDispatchSupplier = isDispatchSupplier;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public BigDecimal getDiversionValue() {
        return diversionValue;
    }

    public Integer getSupplierOrderCount() {
        return supplierOrderCount;
    }

    public DiversionMatchEnum getType() {
        return type;
    }

    public Integer getIsConfigSupplier() {
        return isConfigSupplier;
    }

    public void setDiversionValue(BigDecimal diversionValue) {
        this.diversionValue = diversionValue;
    }

    public boolean isDispatchSupplier() {
        return isDispatchSupplier;
    }

    public boolean isConfigSupplier() {
        return YesOrNo.isYes(isConfigSupplier);
    }

    /**
     * 计算分流比例
     * @param supplierOrderCount
     * @param totalOrderCount
     * @param diversionValue
     * @return
     */
    public static BigDecimal calculate(Integer supplierOrderCount, Integer totalOrderCount, BigDecimal diversionValue) {
        BigDecimal totalCountBig = BigDecimal.valueOf(totalOrderCount);
        BigDecimal supplierCountBig = BigDecimal.valueOf(supplierOrderCount);
        BigDecimal supplierSort = supplierCountBig.divide(totalCountBig, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        //供应商配置的分流订单数 /  所有供应商分流订单数汇总 - 配置分流占比
        return supplierSort.subtract(diversionValue).abs();
    }
}
