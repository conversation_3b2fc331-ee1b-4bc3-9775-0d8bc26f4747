package com.ctrip.dcs.domain.schedule.exception;

import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.igt.framework.common.exception.BizException;

/**
 * 检查项数据校验异常
 * <AUTHOR>
 */
public class CheckItemValidateException extends BizException {

    private ErrorCode errorCode;

    public CheckItemValidateException() {
        this.errorCode = ErrorCode.CHECK_VALIDATE_ERROR;
    }

    public ErrorCode getErrorCode() {
        return errorCode;
    }
}
