package com.ctrip.dcs.domain.schedule.gateway;

import com.ctrip.dcs.domain.common.value.RightAndPointVO;
import com.ctrip.dcs.domain.schedule.dto.QueryPunishReasonDTO;
import com.ctrip.igt.framework.common.result.Result;

/**
 * 用车司机处罚申诉处理服务
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @create 2023/5/27 15:37
 */
public interface CarDriverComplaintServiceGateway {

    /**
     * 检查改派原因是否有责
     */
    Result<RightAndPointVO> checkPunishReasonDraft(QueryPunishReasonDTO params);

}