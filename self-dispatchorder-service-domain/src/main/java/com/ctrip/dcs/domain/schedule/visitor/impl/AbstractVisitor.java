package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.visitor.Visitor;
import com.ctrip.igt.framework.common.spring.InstanceLocator;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public abstract class AbstractVisitor implements Visitor {

    @Override
    public void visit(CheckContext context) {

    }

    @Override
    public void visit(SortContext context) {

    }

    protected final  <T> T getInstance(Class<T> clazz) {
        T instance = InstanceLocator.getInstance(clazz);
        Assert.notNull(instance);
        return instance;
    }

    protected final  <T> T getInstance(String beanName, Class<T> clazz) {
        T instance = InstanceLocator.getInstance(beanName, clazz);
        Assert.notNull(instance);
        return instance;
    }

    protected List<Long> getDriverIds(List<DriverVO> drivers, Set<Long> filter) {
        return filter(drivers, filter).stream()
                .map(DriverVO::getDriverId).collect(Collectors.toList());
    }

    protected List<DriverVO> filter(List<DriverVO> drivers, Set<Long> filter) {
        return drivers.stream()
                .filter(driver -> !filter.contains(driver.getDriverId()))
                .collect(Collectors.toList());
    }
}
