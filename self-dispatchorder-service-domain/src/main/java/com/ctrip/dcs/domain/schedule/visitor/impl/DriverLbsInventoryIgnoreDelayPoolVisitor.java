package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.ConflictGateway;
import com.ctrip.dcs.domain.schedule.value.DrvInventoryCheckResultVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DriverLbsInventoryIgnoreDelayPoolVisitor extends AbstractVisitor {

    private DspOrderVO dspOrder;

    private List<DriverVO> drivers;

    private ConflictGateway conflictGateway;

    public DriverLbsInventoryIgnoreDelayPoolVisitor(DspOrderVO dspOrder, List<DriverVO> drivers) {
        Assert.notNull(dspOrder);
        Assert.notEmpty(drivers);
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.conflictGateway = getInstance(ConflictGateway.class);
    }

    public DriverLbsInventoryIgnoreDelayPoolVisitor(DspOrderVO dspOrder, List<DriverVO> drivers, ConflictGateway conflictGateway) {
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.conflictGateway = conflictGateway;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, DrvInventoryCheckResultVO> map = context.getDrvInventoryIgnoreDelayPoolCheckResMap();
        List<DriverVO> filter = filter(drivers, map.keySet());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        List<Long> driverIds = filter.stream().map(DriverVO::getDriverId).collect(Collectors.toList());
        Map<Long, DrvInventoryCheckResultVO> result = conflictGateway.checkDriverOrderConflict(dspOrder, driverIds, true);
        map.putAll(result);
    }
}
