package com.ctrip.dcs.domain.schedule.factory;

import com.ctrip.dcs.domain.common.enums.DspStage;
import com.ctrip.dcs.domain.schedule.check.CheckChain;
import com.ctrip.dcs.domain.schedule.check.CheckConfig;
import com.ctrip.dcs.domain.schedule.check.GrabCheckChain;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CheckChainFactory {

    /**
     * 创建检查链
     * @param config
     * @param stage
     * @return
     */
    CheckChain create(CheckConfig config, DspStage stage);


    /**
     * 创建检查链
     * @param config
     * @param stage
     * @return
     */
    GrabCheckChain createGrabCheckChain(CheckConfig config, DspStage stage);

    /**
     * 创建检查链
     * 指定检查项
     * @param checkItems
     * @return
     */
    Check<PERSON>hain create(List<String> checkItems);
}
