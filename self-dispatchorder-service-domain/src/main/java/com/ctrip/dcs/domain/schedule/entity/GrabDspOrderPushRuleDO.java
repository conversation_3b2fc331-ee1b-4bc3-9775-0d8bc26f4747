package com.ctrip.dcs.domain.schedule.entity;

import cn.hutool.core.lang.Pair;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class GrabDspOrderPushRuleDO {

    /**
     * ID
     */
    private Long id;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 城市
     */
    private Long cityId;

    /**
     * 品类
     */
    private String categoryCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 预订车组ID集合
     */
    private String vehicleGroupIdList;

    /**
     * 规则类型；1-通用规则，2-特殊时段规则
     */
    private Integer ruleType;

    /**
     * 立即发单时间 分钟
     */
    private Integer immediatePushTime;

    /**
     * 特殊时段-开始时间 HH:mm
     */
    private String startBookTime;

    /**
     * 特殊时段-结束时间 HH:mm
     */
    private String endBookTime;

    /**
     * 固定发单时间 HH:mm
     */
    private List<String> fixedPushTimes;
    /**
     * 特殊时段-用车时间 HH:mm
     */
    private String bookTime;


    /**
     * 是否删除，0-未删除，1-删除
     */
    private Integer idDel;

    /**
     * 加价规则
     */
    private GrabDspOrderPushRewardRuleDO rewardRule;

    private GrabDspOrderPushPriorityRuleDO priorityRule;

    /**
     * 计算通用规则的固定时间
     * @param date
     * @return
     */
    public Date calculateCommonFixedTime(Date date) {
        if (CollectionUtils.isEmpty(fixedPushTimes)) {
            return null;
        }
        List<Date> times = Lists.newArrayList(date, DateUtil.addDays(date, 1));
        for (Date time : times) {
            String day = DateUtil.formatDate(time, DateUtil.DATE_FORMAT);
            for (String fixedPushTime : fixedPushTimes) {
                Date fixedTime = DateUtil.parse(day + " " + fixedPushTime, DateUtil.DATE_MINUTES_FORMAT);
                if (DateUtil.isAfter(fixedTime, date)) {
                    return fixedTime;
                }
            }
        }
        return date;
    }

    /**
     * 计算特殊规则规则的固定时间
     * @param date
     * @return
     */
    public Date calculateSpecialFixedTime(Date date) {
        if (StringUtils.isBlank(bookTime)) {
            return null;
        }
        List<Date> times = Lists.newArrayList(DateUtil.addDays(date, -1), date, DateUtil.addDays(date, 1));
        for (Date time : times) {
            String day = DateUtil.formatDate(time, DateUtil.DATE_FORMAT);
            Date startTime = DateUtil.parse(day + " " + startBookTime, DateUtil.DATE_MINUTES_FORMAT);
            Date endTime = DateUtil.parse(day + " " + endBookTime, DateUtil.DATE_MINUTES_FORMAT);
            if (DateUtil.isAfter(startTime, endTime)) {
                // 跨天
                endTime = DateUtil.addDays(endTime, 1);
            }
            if (DateUtil.isBetween(date, startTime, endTime)) {
                // 在时间段内
                String endDay = DateUtil.formatDate(endTime, DateUtil.DATE_FORMAT);
                Date fixedTime = DateUtil.parseDateStr2Date(endDay + " " + bookTime);
                if (DateUtil.isAfter(fixedTime, date)) {
                    return fixedTime;
                }
                return DateUtil.addDays(fixedTime, 1);
            }
        }
        return null;
    }

    public Long getId() {
        return id;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public Long getCityId() {
        return cityId;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public String getVehicleGroupIdList() {
        return vehicleGroupIdList;
    }

    public Integer getRuleType() {
        return ruleType;
    }

    public Integer getImmediatePushTime() {
        return immediatePushTime;
    }

    public String getStartBookTime() {
        return startBookTime;
    }

    public String getEndBookTime() {
        return endBookTime;
    }

    public List<String> getFixedPushTimes() {
        return fixedPushTimes;
    }

    public Integer getIdDel() {
        return idDel;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public void setVehicleGroupIdList(String vehicleGroupIdList) {
        this.vehicleGroupIdList = vehicleGroupIdList;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public void setImmediatePushTime(Integer immediatePushTime) {
        this.immediatePushTime = immediatePushTime;
    }

    public void setStartBookTime(String startBookTime) {
        this.startBookTime = startBookTime;
    }

    public void setEndBookTime(String endBookTime) {
        this.endBookTime = endBookTime;
    }

    public void setFixedPushTimes(List<String> fixedPushTimes) {
        this.fixedPushTimes = fixedPushTimes;
    }

    public String getBookTime() {
        return bookTime;
    }

    public void setBookTime(String bookTime) {
        this.bookTime = bookTime;
    }

    public void setIdDel(Integer idDel) {
        this.idDel = idDel;
    }

    public GrabDspOrderPushRewardRuleDO getRewardRule() {
        return rewardRule;
    }

    public void setRewardRule(GrabDspOrderPushRewardRuleDO rewardRule) {
        this.rewardRule = rewardRule;
    }

    public GrabDspOrderPushPriorityRuleDO getPriorityRule() {
        return priorityRule;
    }

    public void setPriorityRule(GrabDspOrderPushPriorityRuleDO priorityRule) {
        this.priorityRule = priorityRule;
    }

    public Long calculateDelayPushMinutes(List<String> labels) {
        List<GrabDspOrderPushPriorityRuleItemDO> list = Optional.ofNullable(this.priorityRule).map(GrabDspOrderPushPriorityRuleDO::getItems).orElse(Collections.emptyList()).stream().sorted(Comparator.comparing(GrabDspOrderPushPriorityRuleItemDO::getDelay)).toList();
        for (GrabDspOrderPushPriorityRuleItemDO rule : list) {
            Set<String> code = rule.getData().stream().map(Pair::getKey).collect(Collectors.toSet());
            if (CollectionUtils.containsAny(labels, code)) {
                // 有交集，说明命中了优先级规则
                return rule.getDelay();
            }
        }
        return Optional.ofNullable(priorityRule).map(GrabDspOrderPushPriorityRuleDO::getDefaultDelay).orElse(0L);
    }

    public Double calculateRewardDuration(Date lastConfirmCatTimeBj, Date grabPushTimeBj) {
        if (grabPushTimeBj == null || lastConfirmCatTimeBj == null || this.getRewardRule() == null) {
            return 0D;
        }
        // 加价间隔=（最晚确认司机车辆时间-发单时间）/ 加价轮次 + 1
        return DateUtil.seconds(grabPushTimeBj, lastConfirmCatTimeBj) / (this.getRewardRule().getRound() + 1);
    }
}
