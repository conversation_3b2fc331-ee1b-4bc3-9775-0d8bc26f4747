package com.ctrip.dcs.domain.schedule.process.impl;

import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.exception.OrderStatusException;
import com.ctrip.dcs.domain.common.service.ConfirmDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryVehicleService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory;
import com.ctrip.dcs.domain.schedule.process.Process;
import com.ctrip.dcs.domain.schedule.process.Processor;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.service.RecommendService;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 系统指派
 * <AUTHOR>
 */
@Processor(value = "systemAssignProcess", type = DspType.SYSTEM_ASSIGN)
public class SystemAssignProcess extends BaseProcess implements Process {

    private static final Logger logger = LoggerFactory.getLogger(SystemAssignProcess.class);

    @Autowired
    private RecommendService recommendService;

    @Autowired
    private CheckService checkService;

    @Autowired
    private ConfirmDspOrderService confirmDspOrderService;

    @Autowired
    private DriverOrderFactory driverOrderFactory;

    @Autowired
    private QueryVehicleService queryVehicleService;

    @Autowired
    private QueryDspOrderService queryDspOrderService;
    @Override
    public void execute(ScheduleTaskDO task, DspOrderVO order) {
        boolean confirm = false;
        try {
            // 运力推荐
            List<SortModel> models = recommendService.recommend(order, task.getSubSku(), DuidVO.of(task));
            if (CollectionUtils.isEmpty(models)) {
                logger.info("system assign process", "driver recommend null! task id:{}, dsp order id:{}", task.getTaskId(), task.getDspOrderId());
                MetricsUtil.recordValue(MetricsConstants.DRIVER_RECOMMEND_EMPTY_COUNT);
                return;
            }
            DspOrderVO newOrder = null;
            boolean allConfigDspTypeFlag = queryDspOrderService.getAllConfigDspTypeFlag(task);
            logger.info("system_assign_process_allConfigDspTypeFlag", JsonUtils.toJson(allConfigDspTypeFlag));
            if(allConfigDspTypeFlag){
                newOrder = queryDspOrderService.queryOrderDetail(order.getDspOrderId());
                if(Objects.isNull(newOrder)){
                    logger.error("system assign process", "query order detail fail! task id:{}, dsp order id:{}", task.getTaskId(), task.getDspOrderId());
                    MetricsUtil.recordValue(MetricsConstants.TAKEN_ORDER_DETAIL_NULL);
                    return;
                }
            }
            for (SortModel model : models) {
                if (confirm) {
                    // 应单成功，退出循环
                    break;
                }
                try {
                    DspModelVO modelModel = model.getModel();
                    modelModel.setOrder(Objects.nonNull(newOrder) ? newOrder : modelModel.getOrder());
                    confirm = execute(task, modelModel);
                } catch (OrderStatusException e) {
                    // 应单异常，退出
                    throw e;
                }
            }
        } catch (OrderStatusException e) {
            logger.warn(e);
        } catch (Exception e) {
            MetricsUtil.recordValue(MetricsConstants.DRIVER_RECOMMEND_EMPTY_COUNT);
            logger.error(e);
        } finally {
            completeTask(task, confirm);
        }
    }

    public boolean execute(ScheduleTaskDO task, DspModelVO model) throws OrderStatusException {
        DspOrderVO order = model.getOrder();
        DriverVO driver = model.getDriver();
        TransportGroupVO transportGroup = model.getTransportGroup();
        DriverOrderVO driverOrder = null;
        // 接单检查
        CheckModel check = checkService.check(new TakenCheckCommand(model.getOrder(), task.getSubSku(), model.getDriver(), model.getTransportGroup(), DuidVO.of(task)));
        if (!check.isPass()) {
            logger.info("system assign process", "driver taken check fail! task id:{}, dsp order id:{}, driver id:{}", task.getTaskId(), task.getDspOrderId(), model.getDriver().getDriverId());
            return false;
        }
        // 创建司机单
        driverOrder = driverOrderFactory.create(model, task);
        if (Objects.isNull(driverOrder)) {
            logger.info("system assign process", "driver order create fail! task id:{}, dsp order id:{}, driver id:{}", task.getTaskId(), task.getDspOrderId(), model.getDriver().getDriverId());
            return false;
        }
        VehicleVO vehicle = queryVehicle(driver, CategoryUtils.selfGetParentType(order));
        boolean confirm = false;
        try {
            // 应单
            DriverAndCarConfirmVO confirmVO = DriverAndCarConfirmVO.builder()
                    .dspOrder(order)
                    .serviceProvider(new ServiceProviderVO(order.getSpId()))
                    .supplier(driver.getSupplier())
                    .transportGroup(transportGroup)
                    .driver(driver)
                    .vehicle(vehicle)
                    .duid(DuidVO.of(task))
                    .driverOrderId(driverOrder.getDriverOrderId())
                    .rewardAmount(task.getReward().toString())
                    .event(OrderStatusEvent.SYSTEM_ASSIGN)
                    .operator(OperatorVO.systemOperator())
                    .build();
            confirmDspOrderService.confirm(confirmVO);
            // 应单成功
            confirm = true;
        } catch (OrderStatusException e) {
            logger.warn(e);
            if (ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR.equals(e.getErrorCode())) {
                // 派发单不允许应单异常，则退出。否则，尝试下一个司机。
                throw e;
            }
        } catch (Exception e) {
            logger.error("driver and car confirm error", e);
        } finally {
            if (!confirm) {
                // 未接单成功，取消司机单
                sendDriverOrderConfirmFailMessage(driverOrder);
            }
        }
        return confirm;
    }

    private VehicleVO queryVehicle(DriverVO driver, ParentCategoryEnum parentCategoryEnum) {
        Optional<Long> optional = Optional.ofNullable(driver).map(DriverVO::getCar).map(CarVO::getCarId);
        return optional.map(carId -> queryVehicleService.query(carId,parentCategoryEnum)).orElse(null);
    }

}
