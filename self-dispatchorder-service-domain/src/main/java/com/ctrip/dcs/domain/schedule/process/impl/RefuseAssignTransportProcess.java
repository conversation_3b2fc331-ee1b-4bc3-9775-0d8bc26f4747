package com.ctrip.dcs.domain.schedule.process.impl;

import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.service.IdempotentCheckService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.dsporder.entity.UserOrderDetail;
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand;
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.CreateDispatcherGrabOrderEvent;
import com.ctrip.dcs.domain.schedule.factory.DispatcherGrabOrderFactory;
import com.ctrip.dcs.domain.schedule.gateway.DispatcherGrabOrderGateway;
import com.ctrip.dcs.domain.schedule.process.Process;
import com.ctrip.dcs.domain.schedule.process.Processor;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Processor(value = "refuseAssignTransportProcess", type = DspType.REFUSE_DISPATCHER_ASSIGN)
public class RefuseAssignTransportProcess extends BaseProcess implements Process {

    private static final Logger logger = LoggerFactory.getLogger(RefuseAssignTransportProcess.class);

    private static final String DISPATCHER_GRAB_ORDER_CREATE_PREFIX = SysConstants.APP_ID + "_DISPATCHER_GRAB_ORDER_CREATE_%s_%d_%d";

    @Autowired
    private IdempotentCheckService idempotentCheckService;

    @Autowired
    private DispatcherGrabOrderGateway dispatcherGrabOrderGateway;

    @Autowired
    private MessageProviderService messageProviderService;

    @Autowired
    private CheckService checkService;

    @Autowired
    private DispatcherGrabOrderFactory dispatcherGrabOrderFactory;

    @Autowired
    private IGTOrderQueryServiceGateway igtOrderQueryServiceGateway;

    @Override
    public void execute(ScheduleTaskDO task, DspOrderVO order) {
        try {
            // 运力检查
            List<CheckModel> checkModels = checkService.check(new DspCheckCommand(order, task.getSubSku(), DuidVO.of(task)));
            List<DspModelVO> models = checkModels.stream().filter(CheckModel::isPass).map(CheckModel::getModel).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(models)) {
                logger.info("refuse assign transport process", "transport recommend null! task id:{}, dsp order id:{}", task.getTaskId(), task.getDspOrderId());
                return;
            }
            List<TransportGroupVO> transportGroups = models.stream().map(DspModelVO::getTransportGroup)
                    .filter(transportGroup -> transportGroup != null && transportGroup.getTransportGroupMode() != null)
                    .filter(transportGroup -> transportGroup.getTransportGroupMode().isManual())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(transportGroups)) {
                logger.info("refuse assign transport process", "transport recommend null! task id:{}, dsp order id:{}", task.getTaskId(), task.getDspOrderId());
                return;
            }
            List<DispatcherGrabOrderDO> list = dispatcherGrabOrderFactory.create(task, order, transportGroups);
            execute(list);
        } catch (Exception e) {
            MetricsUtil.recordValue(MetricsConstants.REFUSE_ASSIGN_TRANSPORT_ERROR_COUNT);
            logger.error(e);
        } finally {
            completeTask(task, false);
        }
    }

    public void execute(List<DispatcherGrabOrderDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            logger.info("CreateDispatcherGrabOrderExeCmdInfo", "no grab orders");
            return;
        }
        // 过滤重复的订单
        List<DispatcherGrabOrderDO> orders =  filter(list);
        if (CollectionUtils.isEmpty(orders)) {
            logger.info("CreateDispatcherGrabOrderExeCmdInfo", "no new grab orders");
            return;
        }
        try {
            setOrderTime(orders);
            // 保存运力组待抢订单
            orders = dispatcherGrabOrderGateway.create(orders);
        } catch (Exception e) {
            logger.error("CreateDispatcherGrabOrderExeCmdError", e);
            throw ErrorCode.CREATE_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        }
        // 发送消息
        post(orders);
    }

    //设置用户订单下单时间
    public void setOrderTime(List<DispatcherGrabOrderDO> orders) {
        for(DispatcherGrabOrderDO orderDO:orders){
            UserOrderDetail userOrderDetail = igtOrderQueryServiceGateway.queryUserOrderDetail(orderDO.getUserOrderId());
            if(userOrderDetail != null && userOrderDetail.getBookInfo() != null && StringUtils.isNotEmpty(userOrderDetail.getBookInfo().getOrderTimeLocal())){
                orderDO.setOrderTime(DateUtil.parseDateStr2Date(userOrderDetail.getBookInfo().getOrderTimeLocal()));
            }
        }
    }

    private void post(List<DispatcherGrabOrderDO> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return;
        }
        List<Long> ids = pos.stream().map(DispatcherGrabOrderDO::getId).collect(Collectors.toList());
        messageProviderService.send(new CreateDispatcherGrabOrderEvent(ids));
    }

    private List<DispatcherGrabOrderDO> filter(List<DispatcherGrabOrderDO> list) {
        return list.stream().filter(this::idempotent).collect(Collectors.toList());
    }

    private boolean idempotent(DispatcherGrabOrderDO grabOrder) {
        String key = generateKey(grabOrder);
        long expire = generateExpire(grabOrder);
        boolean isProcessed = idempotentCheckService.isNotProcessed(key, expire);
        if (!isProcessed) {
            return false;
        }
        DispatcherGrabOrderDO order = dispatcherGrabOrderGateway.query(grabOrder.getDspOrderId(), grabOrder.getSupplierId());
        return Objects.isNull(order);
    }

    private String generateKey(DispatcherGrabOrderDO grabOrder) {
        return String.format(DISPATCHER_GRAB_ORDER_CREATE_PREFIX, grabOrder.getUserOrderId(), grabOrder.getSupplierId(), grabOrder.getModifyVersion());
    }

    private long generateExpire(DispatcherGrabOrderDO grabOrder) {
        long seconds = (long) DateUtil.seconds(new Date(), grabOrder.getLastConfirmTime());
        return Math.max(seconds, 1L);
    }
}
