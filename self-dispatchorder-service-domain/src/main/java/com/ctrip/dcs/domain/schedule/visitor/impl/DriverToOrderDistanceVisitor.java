package com.ctrip.dcs.domain.schedule.visitor.impl;

import com.ctrip.dcs.domain.common.service.QueryDriverLocationService;
import com.ctrip.dcs.domain.common.util.LocalCollectionUtils;
import com.ctrip.dcs.domain.common.value.DriverOrderLocationVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.DriverDomainServiceGateway;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DriverToOrderDistanceVisitor extends AbstractVisitor {

    /**
     * 查询参数 司机点位 信息
     */
    private List<DriverOrderLocationVO> driverOrderLocationVOS;
    /**
     * 位置服务
     */
    private QueryDriverLocationService queryDriverLocationService;

    public DriverToOrderDistanceVisitor(List<DriverOrderLocationVO> driverOrderLocationVOS) {
        this.driverOrderLocationVOS = driverOrderLocationVOS;
        this.queryDriverLocationService = getInstance(QueryDriverLocationService.class);
    }

    public DriverToOrderDistanceVisitor(List<DriverOrderLocationVO> driverOrderLocationVOS, QueryDriverLocationService queryDriverLocationService) {
        this.driverOrderLocationVOS = driverOrderLocationVOS;
        this.queryDriverLocationService = queryDriverLocationService;
    }

    @Override
    public void visit(CheckContext context) {
        //查询司机距离订单用车地点的距离
        queryDriverToOrderDis(context);
    }

    /**
     * 查询司机距离订单用车地点的距离
     * @param context
     */
    private void queryDriverToOrderDis(CheckContext context){
        if(LocalCollectionUtils.isEmpty(driverOrderLocationVOS)){
            return;
        }
        List<DriverOrderLocationVO> resultList = queryDriverLocationService.queryDriverToOrderDis(driverOrderLocationVOS);
        if(LocalCollectionUtils.isEmpty(resultList)){
            return;
        }
        Map<String, BigDecimal> distanceMap = new HashMap<>();
        for (DriverOrderLocationVO driverOrderLocationVO : resultList) {
            String key = driverOrderLocationVO.getDriverId().toString() + driverOrderLocationVO.getOrderId();
            distanceMap.put(key,driverOrderLocationVO.getDistance());
        }
        context.getDriverToOrderDistanceMap().putAll(distanceMap);
    }
}
