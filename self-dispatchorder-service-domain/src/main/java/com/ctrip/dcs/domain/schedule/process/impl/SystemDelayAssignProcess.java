package com.ctrip.dcs.domain.schedule.process.impl;

import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.exception.OrderStatusException;
import com.ctrip.dcs.domain.common.service.ConfirmDspOrderService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryVehicleService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand;
import com.ctrip.dcs.domain.schedule.check.source.CheckSource;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.CompleteTaskEvent;
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory;
import com.ctrip.dcs.domain.schedule.gateway.DspDelayGateway;
import com.ctrip.dcs.domain.schedule.process.Process;
import com.ctrip.dcs.domain.schedule.process.Processor;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.service.RecommendService;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.value.DelayOrderVO;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 系统指派运力组
 * <AUTHOR>
 */
@Processor(value = "systemDelayAssignProcess", type = DspType.DELAY_ASSIGN)
public class SystemDelayAssignProcess extends BaseProcess implements Process {

    private static final Logger logger = LoggerFactory.getLogger(SystemDelayAssignProcess.class);

    @Autowired
    private ScheduleTaskRepository taskRepository;

    @Autowired
    private RecommendService recommendService;

    @Autowired
    private ConfirmDspOrderService confirmDspOrderService;

    @Autowired
    private MessageProviderService messageProducer;

    @Autowired
    private DspDelayGateway dspDelayGateway;

    @Autowired
    private DriverOrderFactory driverOrderFactory;

    @Autowired
    private CheckService checkService;

    @Autowired
    private QueryVehicleService queryVehicleService;

    @Override
    public void execute(ScheduleTaskDO task, DspOrderVO order) {
        boolean confirm = false;
        DriverOrderVO driverOrder = null;
        try {
            // 入池
            if (!Objects.equals(order.getShortDisOrder(), com.ctrip.dcs.domain.dsporder.YesOrNo.YES.getCode())
                    && Objects.equals(task.getSubSku().getCheck().getCheckSourceId(), CheckSource.SHORT_DISTANCE_DRIVER_CHECK_SOURCE)) {
                logger.info("delay_dsp_short_order", "The order does not match the short kilometer strategy.dspOrderId={}",order.getDspOrderId());
                return;
            }
            if (Objects.equals(order.getShortDisOrder(), com.ctrip.dcs.domain.dsporder.YesOrNo.YES.getCode())
                    && Objects.equals(task.getSubSku().getCheck().getCheckSourceId(), CheckSource.DRIVER_CHECK_SOURCE)) {
                logger.info("delay_dsp_short_order_1", "The order does not match the short kilometer strategy.dspOrderId={}",order.getDspOrderId());
                return;
            }
            DelayOrderVO delayOrder = dspDelayGateway.insert(order, task);
            if (!delayOrder.isDelay()) {
                return;
            }
            DriverVO driver = null;
            String driverOrderId = null;
            if (delayOrder.isVirtualDsp()) {
                // 虚拟派
                DspModelVO model = virtualDsp(task, order);
                if (Objects.nonNull(model)) {
                    driver = model.getDriver();
                    // 创建司机单
                    driverOrder = driverOrderFactory.create(model, task);
                    driverOrderId = Optional.ofNullable(driverOrder).map(DriverOrderVO::getDriverOrderId).orElse(StringUtils.EMPTY);
                }
            }
            ServiceProviderConfirmVO confirmVO = ServiceProviderConfirmVO.builder()
                    .dspOrder(order)
                    .serviceProvider(new ServiceProviderVO(order.getSpId()))
                    .duid(DuidVO.of(task))
                    .driver(driver)
                    .driverOrderId(driverOrderId)
                    .event(OrderStatusEvent.SYSTEM_ASSIGN)
                    .delayExecuteTimeDeadline(DateUtil.parseDateStr2Date(delayOrder.getExecuteTimeDeadline()))
                    .build();
            // 应单
            confirmDspOrderService.confirm(confirmVO);
            confirm = true;
        } catch (OrderStatusException e) {
            logger.error(e);
            if (ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR.equals(e.getErrorCode())) {
                // 派发单不允许应单异常，则推出。否则，尝试下一个司机。
                throw ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR.getBizException();
            }
        } catch (Exception e) {
            logger.error("service provider confirm error", e);
        } finally {
            cancelDriverOrder(driverOrder, confirm);
            completeTask(task, confirm);
        }
    }

    @Override
    protected void completeTask(ScheduleTaskDO task, Boolean confirm) {
        if (confirm) {
            // 任务完成
            task.complete();
            taskRepository.complete(task);
        } else {
            // 任务取消
            task.cancel();
            taskRepository.cancel(task);
        }

        // 发送任务完成消息
        messageProducer.send(new CompleteTaskEvent(task.getDspOrderId(), task.getScheduleId(), task.getTaskId(), task.getScheduleTaskId(), YesOrNo.valueOf(confirm).getCode()));
    }

    /**
     * 确认失败，取消司机单
     * @param driverOrder
     * @param confirm
     */
    private void cancelDriverOrder(DriverOrderVO driverOrder, boolean confirm) {
        if (!confirm) {
            sendDriverOrderConfirmFailMessage(driverOrder);
        }
    }

    /**
     * 虚拟派
     * @param task
     * @return
     */
    private DspModelVO virtualDsp(ScheduleTaskDO task, DspOrderVO order) {
        try {
            List<SortModel> models = recommendService.recommend(order, task.getSubSku(), DuidVO.of(task));
            return models.stream().map(SortModel::getModel).findFirst().orElse(null);
        } catch (Exception e) {
            logger.error("virtual dsp error", e);
        }
        return null;
    }


    /**
     * 延后派专用接单
     * @param task
     * @param model
     * @return
     * @throws OrderStatusException
     */
    public boolean dspTakenExecute(ScheduleTaskDO task, DspModelVO model){
        boolean confirm = false;
        DriverOrderVO driverOrder = null;
        try {
            // 创建司机单
            driverOrder = driverOrderFactory.createForDelay(model, task);
            if (Objects.isNull(driverOrder)) {
                MetricsUtil.recordValue("delay.createDriverOrder.fail", 1);
                logger.info("SystemDelayAssignProcess_dspTakenExecute", "driver order create fail! task id:{}, dsp order id:{}, driver id:{}", task.getTaskId(), task.getDspOrderId(), model.getDriver().getDriverId());
                throw ErrorCode.CREATE_DRIVER_ORDER_ERROR.getBizException();
            }
            DspOrderVO order = model.getOrder();
            DriverVO driver = model.getDriver();
            TransportGroupVO transportGroup = model.getTransportGroup();
            // 接单检查
            CheckModel check = checkService.check(new TakenCheckCommand(model.getOrder(), task.getSubSku(), model.getDriver(), model.getTransportGroup(), DuidVO.of(task)));
            if (!check.isPass()) {
                logger.info("SystemDelayAssignProcess_dspTakenExecute", "driver taken check fail! task id:{}, dsp order id:{}, driver id:{}", task.getTaskId(), task.getDspOrderId(), model.getDriver().getDriverId());
                throw new BizException(String.valueOf(check.getCheckCode().getCode()), check.getCheckCode().getDesc());
            }
            VehicleVO vehicle = queryVehicle(driver, CategoryUtils.selfGetParentType(order));
            // 应单
            DriverAndCarConfirmVO confirmVO = DriverAndCarConfirmVO.builder()
                    .dspOrder(order)
                    .serviceProvider(new ServiceProviderVO(order.getSpId()))
                    .supplier(driver.getSupplier())
                    .transportGroup(transportGroup)
                    .driver(driver)
                    .vehicle(vehicle)
                    .duid(DuidVO.of(task))
                    .driverOrderId(driverOrder.getDriverOrderId())
                    .rewardAmount(task.getReward().toString())
                    .event(OrderStatusEvent.SYSTEM_ASSIGN)
                    .operator(OperatorVO.systemOperator())
                    .build();
            confirmDspOrderService.confirm(confirmVO);
            // 应单成功
            confirm = true;
        } catch (OrderStatusException e) {
            logger.error(e);
            if (ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR.equals(e.getErrorCode())) {
                // 派发单不允许应单异常，则退出。否则，尝试下一个司机。
                throw ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR.getBizException();
            }
        }  catch (BizException e) {
            logger.info("SystemDelayAssignProcess_dspTakenExecute_error", e);
            throw new BizException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("SystemDelayAssignProcess_dspTakenExecute_error", e);
            throw e;
        } finally {
            if (!confirm) {
                // 未接单成功，取消司机单
                sendDriverOrderConfirmFailMessage(driverOrder);
            }
        }
        return confirm;
    }

    private VehicleVO queryVehicle(DriverVO driver, ParentCategoryEnum parentCategoryEnum) {
        Optional<Long> optional = Optional.ofNullable(driver).map(DriverVO::getCar).map(CarVO::getCarId);
        return optional.map(carId -> queryVehicleService.query(carId,parentCategoryEnum)).orElse(null);
    }
}
