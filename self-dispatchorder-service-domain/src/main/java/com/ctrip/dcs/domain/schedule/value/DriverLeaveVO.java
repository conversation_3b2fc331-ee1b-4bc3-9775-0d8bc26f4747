package com.ctrip.dcs.domain.schedule.value;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DriverLeaveVO {

    private Long driverId;
    /**
     * 请假开始时间
     */
    private String leaveBeginTime;
    /**
     * 请假结束时间
     */
    private String leaveEndTime;

}
