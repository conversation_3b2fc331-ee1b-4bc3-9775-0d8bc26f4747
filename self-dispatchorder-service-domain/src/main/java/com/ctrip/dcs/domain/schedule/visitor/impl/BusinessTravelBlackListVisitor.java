package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.dsporder.gateway.ChannelGateway;
import com.ctrip.dcs.domain.dsporder.value.DistributionChannelVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.BusinessTravelServiceGateway;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
public class BusinessTravelBlackListVisitor extends AbstractVisitor {

    private DspOrderVO order;

    private ChannelGateway channelGateway;

    private BusinessTravelServiceGateway businessTravelServiceGateway;

    public BusinessTravelBlackListVisitor(DspOrderVO order) {
        Assert.notNull(order);
        Assert.notNull(order.getDistributionChannelId());
        Assert.notBlank(order.getUserOrderId());
        Assert.notNull(order.getEstimatedUseTime());
        this.order = order;
        this.channelGateway = getInstance(ChannelGateway.class);
        this.businessTravelServiceGateway = getInstance(BusinessTravelServiceGateway.class);
    }

    public BusinessTravelBlackListVisitor(DspOrderVO order, ChannelGateway channelGateway, BusinessTravelServiceGateway businessTravelServiceGateway) {
        this.order = order;
        this.channelGateway = channelGateway;
        this.businessTravelServiceGateway = businessTravelServiceGateway;
    }

    @Override
    public void visit(CheckContext context) {
        List<String> blackList = context.getBusinessTravelBlackList();
        Boolean channel = channelGateway.findDistributionChannel(this.order.getDistributionChannelId());
        if (channel) {
            List<String> list = businessTravelServiceGateway.queryBusinessTravelBlackList(this.order.getUserOrderId(), this.order.getEstimatedUseTime());
            if (!CollectionUtils.isEmpty(list)) {
                blackList.addAll(list);
            }
        }
    }
}
