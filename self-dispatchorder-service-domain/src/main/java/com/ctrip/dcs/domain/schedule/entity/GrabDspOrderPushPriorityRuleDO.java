package com.ctrip.dcs.domain.schedule.entity;

import cn.hutool.core.lang.Pair;
import com.ctrip.dcs.domain.common.enums.GrabDspOrderPushRewardType;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GrabDspOrderPushPriorityRuleDO {

    private List<GrabDspOrderPushPriorityRuleItemDO> items;

    private Long defaultDelay;

    public List<GrabDspOrderPushPriorityRuleItemDO> getItems() {
        return items;
    }

    public void setItems(List<GrabDspOrderPushPriorityRuleItemDO> items) {
        this.items = items;
    }

    public Long getDefaultDelay() {
        return defaultDelay;
    }

    public void setDefaultDelay(Long defaultDelay) {
        this.defaultDelay = defaultDelay;
    }
}
