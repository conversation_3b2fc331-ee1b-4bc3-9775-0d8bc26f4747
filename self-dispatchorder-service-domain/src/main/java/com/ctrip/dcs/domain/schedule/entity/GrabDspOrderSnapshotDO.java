package com.ctrip.dcs.domain.schedule.entity;

import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.OrderExtendInfoUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.common.value.OrderWayPointVO;
import com.ctrip.dcs.domain.common.value.XproductVO;
import com.ctrip.dcs.domain.common.value.graborder.ProductInfoDTO;
import com.ctrip.dcs.domain.dsporder.entity.FromPoiDTO;
import com.ctrip.dcs.domain.dsporder.entity.ToPoiDTO;
import com.ctrip.dcs.domain.dsporder.entity.UserCountDTO;
import com.ctrip.dcs.domain.dsporder.value.OrderSettlePriceVO;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.handler.GrabOrderPushRuleContext;
import com.ctrip.dcs.domain.schedule.handler.GrabOrderPushRuleHandler;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class GrabDspOrderSnapshotDO {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户订单id
     */
    private String userOrderId;

    /**
     * 供应单号
     */
    private String supplyOrderId;

    /**
     * 派发单id
     */
    private String dspOrderId;

    /**
     * 司机id
     */
    private Long driverId;

    private String uid;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 城市
     */
    private Long cityId;

    private Integer bizAreaType;

    private BigDecimal specialTimePoint;

    /**
     * 派发单状态
     */
    private Integer orderStatus;

    /**
     * 派发单版本
     */
    private Integer orderVersion;

    /**
     * 品类
     */
    private String categoryCode;

    /**
     * 乘客出场buffer
     */
    private Integer userChoseBuffer;

    private Integer passengerOutBuffer;

    /**
     * duid
     */
    private String duid;

    /**
     * 预订车组ID
     */
    private Long vehicleGroupId;

    /**
     * 预订车组名称
     */
    private String vehicleGroupName;

    /**
     * 预估里程-公里
     */
    private BigDecimal estimatedKm;

    /**
     * 预估时长-分钟
     */
    private BigDecimal estimatedMin;

    /**
     * 预计用车时间（当地）
     */
    private Date estimatedUseTime;

    /**
     * 预计用车时间（北京）
     */
    private Date estimatedUseTimeBj;

    /**
     * 预计完成时间（当地）
     */
    private Date predicServiceStopTime;

    /**
     * 预计完成时间（北京）
     */
    private Date predicServiceStopTimeBj;

    /**
     * 最晚确认车辆时间(当地)
     */
    private Date lastConfirmCarTime;

    /**
     * 最晚确认车辆时间(北京)
     */
    private Date lastConfirmCarTimeBj;

    /**
     * 派发单下单时间（北京）
     */
    private Date createDspOrderTimeBj;

    /**
     * 等待时长
     */
    private Integer waitMinute;

    /**
     * 是否跟司机结算，0-否，1-是
     */
    private Integer settleToDriver;

    /**
     * 1-预估价，2-到手价
     */
    private Integer productType;

    /**
     * 预定人信息
     */
    private UserCountDTO userCountInfo;

    /**
     * 附加服务信息
     */
    private List<XproductVO> orderPackageInfo;

    /**
     * 出发地
     */
    private FromPoiDTO fromPoiInfo;

    /**
     * 出发地
     */
    private ToPoiDTO toPoiInfo;

    /**
     * 商品信息
     */
    private ProductInfoDTO orderProductInfo;

    /**
     * 结算信息
     */
    private List<OrderSettlePriceVO> orderSettlePrices;

    /**
     * 途径点
     */
    private List<OrderWayPointVO> wayPointList;

    /**
     * 旺季涨价金额
     */
    private BigDecimal peakSeasonIncreaseMoney;

    /**
     * 下单时供应币种
     */
    private String supplierCurrency;

    /**
     * 下单时司机币种
     */
    private String driverCurrency;

    /**
     * 点击后toaster提示：系统X秒后会告诉您抢单结果 X从派单获取
     */
    private Integer tipsDelaySecond;

    /**
     * 抢单类型 1-系统触发，2-调度触发,3-人工触发
     */
    private GrabDspOrderSnapshotTypeEnum grabType;

    /**
     * 抢单状态 10-预约抢单；20-抢单中；30-完成;40-取消
     */
    private GrabDspOrderSnapshotStatusEnum grabStatus;

    /**
     * 发单时间-当地
     */
    private Date grabPushTimeLocal;

    /**
     * 发单时间-北京
     */
    private Date grabPushTimeBj;

    /**
     * 是否变更发单时间
     */
    private Boolean isUpdateGrabPushTime = Boolean.FALSE;

    /**
     * 司机可见备注
     */
    private String driverVisibleRemark;

    /**
     * 创建时间
     */
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public String getUserOrderId() {
        return userOrderId;
    }

    public String getDspOrderId() {
        return dspOrderId;
    }

    public Long getDriverId() {
        return driverId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public Long getCityId() {
        return cityId;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public Integer getOrderVersion() {
        return orderVersion;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public Integer getUserChoseBuffer() {
        return userChoseBuffer;
    }

    public String getDuid() {
        return duid;
    }

    public Long getVehicleGroupId() {
        return vehicleGroupId;
    }

    public String getVehicleGroupName() {
        return vehicleGroupName;
    }

    public BigDecimal getEstimatedKm() {
        return estimatedKm;
    }

    public BigDecimal getEstimatedMin() {
        return estimatedMin;
    }

    public Date getEstimatedUseTime() {
        return estimatedUseTime;
    }

    public Date getEstimatedUseTimeBj() {
        return estimatedUseTimeBj;
    }

    public Integer getWaitMinute() {
        return waitMinute;
    }

    public Integer getSettleToDriver() {
        return settleToDriver;
    }

    public Integer getProductType() {
        return productType;
    }

    public UserCountDTO getUserCountInfo() {
        return userCountInfo;
    }

    public List<XproductVO> getOrderPackageInfo() {
        return orderPackageInfo;
    }

    public FromPoiDTO getFromPoiInfo() {
        return fromPoiInfo;
    }

    public ToPoiDTO getToPoiInfo() {
        return toPoiInfo;
    }

    public ProductInfoDTO getOrderProductInfo() {
        return orderProductInfo;
    }

    public GrabDspOrderSnapshotTypeEnum getGrabType() {
        return grabType;
    }

    public GrabDspOrderSnapshotStatusEnum getGrabStatus() {
        return grabStatus;
    }

    public Date getGrabPushTimeLocal() {
        return grabPushTimeLocal;
    }

    public Date getGrabPushTimeBj() {
        return grabPushTimeBj;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public List<OrderSettlePriceVO> getOrderSettlePrices() {
        return orderSettlePrices;
    }

    public Integer getBizAreaType() {
        return bizAreaType;
    }

    public BigDecimal getSpecialTimePoint() {
        return specialTimePoint;
    }

    public List<OrderWayPointVO> getWayPointList() {
        return wayPointList;
    }

    public Integer getPassengerOutBuffer() {
        return passengerOutBuffer;
    }

    public Integer getTipsDelaySecond() {
        return tipsDelaySecond;
    }

    public Date getLastConfirmCarTime() {
        return lastConfirmCarTime;
    }

    public Date getLastConfirmCarTimeBj() {
        return lastConfirmCarTimeBj;
    }

    public Date getCreateDspOrderTimeBj() {
        return createDspOrderTimeBj;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setUserOrderId(String userOrderId) {
        this.userOrderId = userOrderId;
    }

    public void setDspOrderId(String dspOrderId) {
        this.dspOrderId = dspOrderId;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public void setBizAreaType(Integer bizAreaType) {
        this.bizAreaType = bizAreaType;
    }

    public void setSpecialTimePoint(BigDecimal specialTimePoint) {
        this.specialTimePoint = specialTimePoint;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public void setOrderVersion(Integer orderVersion) {
        this.orderVersion = orderVersion;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public void setUserChoseBuffer(Integer userChoseBuffer) {
        this.userChoseBuffer = userChoseBuffer;
    }

    public void setPassengerOutBuffer(Integer passengerOutBuffer) {
        this.passengerOutBuffer = passengerOutBuffer;
    }

    public void setDuid(String duid) {
        this.duid = duid;
    }

    public void setVehicleGroupId(Long vehicleGroupId) {
        this.vehicleGroupId = vehicleGroupId;
    }

    public void setVehicleGroupName(String vehicleGroupName) {
        this.vehicleGroupName = vehicleGroupName;
    }

    public void setEstimatedKm(BigDecimal estimatedKm) {
        this.estimatedKm = estimatedKm;
    }

    public void setEstimatedMin(BigDecimal estimatedMin) {
        this.estimatedMin = estimatedMin;
    }

    public void setEstimatedUseTime(Date estimatedUseTime) {
        this.estimatedUseTime = estimatedUseTime;
    }

    public void setEstimatedUseTimeBj(Date estimatedUseTimeBj) {
        this.estimatedUseTimeBj = estimatedUseTimeBj;
    }

    public void setLastConfirmCarTime(Date lastConfirmCarTime) {
        this.lastConfirmCarTime = lastConfirmCarTime;
    }

    public void setLastConfirmCarTimeBj(Date lastConfirmCarTimeBj) {
        this.lastConfirmCarTimeBj = lastConfirmCarTimeBj;
    }

    public void setCreateDspOrderTimeBj(Date createDspOrderTimeBj) {
        this.createDspOrderTimeBj = createDspOrderTimeBj;
    }

    public void setWaitMinute(Integer waitMinute) {
        this.waitMinute = waitMinute;
    }

    public void setSettleToDriver(Integer settleToDriver) {
        this.settleToDriver = settleToDriver;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public void setUserCountInfo(UserCountDTO userCountInfo) {
        this.userCountInfo = userCountInfo;
    }

    public void setOrderPackageInfo(List<XproductVO> orderPackageInfo) {
        this.orderPackageInfo = orderPackageInfo;
    }

    public void setFromPoiInfo(FromPoiDTO fromPoiInfo) {
        this.fromPoiInfo = fromPoiInfo;
    }

    public void setToPoiInfo(ToPoiDTO toPoiInfo) {
        this.toPoiInfo = toPoiInfo;
    }

    public void setOrderProductInfo(ProductInfoDTO orderProductInfo) {
        this.orderProductInfo = orderProductInfo;
    }

    public void setOrderSettlePrices(List<OrderSettlePriceVO> orderSettlePrices) {
        this.orderSettlePrices = orderSettlePrices;
    }

    public void setWayPointList(List<OrderWayPointVO> wayPointList) {
        this.wayPointList = wayPointList;
    }

    public void setTipsDelaySecond(Integer tipsDelaySecond) {
        this.tipsDelaySecond = tipsDelaySecond;
    }

    public void setGrabType(GrabDspOrderSnapshotTypeEnum grabType) {
        this.grabType = grabType;
    }

    public void setGrabStatus(GrabDspOrderSnapshotStatusEnum grabStatus) {
        this.grabStatus = grabStatus;
    }

    public void setGrabPushTimeLocal(Date grabPushTimeLocal) {
        this.grabPushTimeLocal = grabPushTimeLocal;
    }

    public void setGrabPushTimeBj(Date grabPushTimeBj) {
        this.grabPushTimeBj = grabPushTimeBj;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getSupplyOrderId() {
        return supplyOrderId;
    }

    public void setSupplyOrderId(String supplyOrderId) {
        this.supplyOrderId = supplyOrderId;
    }

    public BigDecimal getPeakSeasonIncreaseMoney() {
        return peakSeasonIncreaseMoney;
    }

    public void setPeakSeasonIncreaseMoney(BigDecimal peakSeasonIncreaseMoney) {
        this.peakSeasonIncreaseMoney = peakSeasonIncreaseMoney;
    }

    public String getSupplierCurrency() {
        return supplierCurrency;
    }

    public void setSupplierCurrency(String supplierCurrency) {
        this.supplierCurrency = supplierCurrency;
    }

    public String getDriverCurrency() {
        return driverCurrency;
    }

    public void setDriverCurrency(String driverCurrency) {
        this.driverCurrency = driverCurrency;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Boolean isUpdateGrabPushTime() {
        return isUpdateGrabPushTime;
    }

    public Date getPredicServiceStopTime() {
        return predicServiceStopTime;
    }

    public void setPredicServiceStopTime(Date predicServiceStopTime) {
        this.predicServiceStopTime = predicServiceStopTime;
    }

    public Date getPredicServiceStopTimeBj() {
        return predicServiceStopTimeBj;
    }

    public void setPredicServiceStopTimeBj(Date predicServiceStopTimeBj) {
        this.predicServiceStopTimeBj = predicServiceStopTimeBj;
    }

    public String getDriverVisibleRemark() {
        return driverVisibleRemark;
    }

    public void setDriverVisibleRemark(String driverVisibleRemark) {
        this.driverVisibleRemark = driverVisibleRemark;
    }

    /**
     * 更新订单信息
     * @param order
     * @return
     */
    public GrabDspOrderSnapshotDO updateOrderDetail(DspOrderVO order) {
        this.userOrderId = order.getUserOrderId();
        this.dspOrderId = order.getDspOrderId();
        if (order.getSupplierId() != null && order.getSupplierId() > 0) {
            this.supplierId = order.getSupplierId().longValue();
        }
        if (this.grabStatus == null) {
            this.grabStatus = GrabDspOrderSnapshotStatusEnum.INIT;
        }
        Map<String, Object> extendInfoMap = order.parseExtendInfo();
        this.cityId = ObjectUtils.defaultIfNull(order.getCityId(), 0).longValue();
        this.bizAreaType = order.getBizAreaType();
        this.orderStatus = order.getOrderStatus();
        this.orderVersion = order.getOrderVersion();
        this.categoryCode = ObjectUtils.defaultIfNull(order.getCategoryCode(), CategoryCodeEnum.ALL).getType();
        this.passengerOutBuffer = MapUtils.getInteger(extendInfoMap, SysConstants.Order.SELF_BUFFER);
        this.userChoseBuffer = MapUtils.getInteger(extendInfoMap, SysConstants.Order.SELF_BUFFER);
        this.vehicleGroupId = order.getOrderCarTypeId();
        this.estimatedKm = order.getEstimatedKm();
        this.estimatedMin = order.getEstimatedMin();
        this.estimatedUseTime = order.getEstimatedUseTime();
        this.estimatedUseTimeBj = order.getEstimatedUseTimeBj();
        this.predicServiceStopTime = order.getPredicServiceStopTime();
        this.predicServiceStopTimeBj = order.getPredicServiceStopTimeBj();
        this.lastConfirmCarTime = order.getLastConfirmCarTime();
        this.lastConfirmCarTimeBj = order.getLastConfirmCarTimeBj();
        this.waitMinute = order.getWaitMinute();
        this.settleToDriver = order.getSettleToDriver();
        this.productType = order.getProductType();
        this.userCountInfo = new UserCountDTO(order.getAdultCount(), order.getChildCount(), order.getBagCount(), order.getMaxBagCount());
        this.orderPackageInfo = order.getXproductList();
        this.fromPoiInfo = new FromPoiDTO(order.getFromCityId(), order.getFromName(), order.getFromAddress(), order.getActualFromCoordsys(), order.getActualFromLongitude(), order.getActualFromLatitude());
        this.toPoiInfo = new ToPoiDTO(order.getToCityId(), order.getToName(), order.getToAddress(), order.getActualToCoordsys(), order.getActualToLongitude(), order.getActualToLatitude());
        Double packageKilometers = Optional.of(order).map(DspOrderVO::getPackageKilometers).map(BigDecimal::doubleValue).orElse(null);
        Double packageHours = Optional.of(order).map(DspOrderVO::getPackageHours).map(BigDecimal::doubleValue).orElse(null);
        this.orderProductInfo = new ProductInfoDTO(order.getSkuId().longValue(), order.getProductName(), order.getProductCode(), order.getUseDays().toDouble(), order.getGiveFreeTime(), packageKilometers, packageHours);
        this.wayPointList = order.getWayPointList();
        this.createDspOrderTimeBj = order.getCreateTime();
        this.supplyOrderId = order.getSupplyOrderId();
        this.peakSeasonIncreaseMoney = order.getPeakSeasonIncreaseMoney();
        this.supplierCurrency = order.getSupplierCurrency();
        this.driverCurrency = order.getDriverCurrency();
        this.uid = order.getUid();
        this.driverVisibleRemark = OrderExtendInfoUtil.getExtendInfo(order, SysConstants.Order.DRIVER_REMARK, StringUtils.EMPTY);
        return this;
    }

    public GrabDspOrderSnapshotDO updateScheduleDetail(ScheduleDO schedule) {
        GrabDspOrderSnapshotTypeEnum type = GrabDspOrderSnapshotTypeEnum.valueOf(schedule.getType().getCode());
        if (this.grabType != null && !Objects.equals(this.grabType, type)) {
            // 新的类型触发了播报，重置抢单状态。例如：人工调度触发了播报，重置抢单状态
            this.grabStatus = GrabDspOrderSnapshotStatusEnum.GRAB;
        }
        this.grabType = type;
        return this;
    }

    public GrabDspOrderSnapshotDO updateScheduleTaskDetail(ScheduleTaskDO task) {
        this.duid = DuidVO.of(task).toString();
        return this;
    }

    public GrabDspOrderSnapshotDO updateSpecialTimePoint(BigDecimal specialTimePoint) {
        this.specialTimePoint = specialTimePoint;
        return this;
    }

    public GrabDspOrderSnapshotDO updateTipsTime(Integer tipsDelaySecond) {
        this.tipsDelaySecond = tipsDelaySecond;
        return this;
    }

    public GrabDspOrderSnapshotDO addOrderSettlePrices(List<OrderSettlePriceVO> orderSettlePrices) {
        if (CollectionUtils.isEmpty(this.orderSettlePrices)) {
            this.orderSettlePrices = Lists.newArrayList();
        }
        Set<Long> supplierIds = this.orderSettlePrices.stream().map(OrderSettlePriceVO::getSupplierId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(orderSettlePrices)) {
            for (OrderSettlePriceVO orderSettlePrice : orderSettlePrices) {
                // 根据供应商去重
                if (!supplierIds.contains(orderSettlePrice.getSupplierId())) {
                    this.orderSettlePrices.add(orderSettlePrice);
                }
            }
        }
        return this;
    }

    public GrabDspOrderSnapshotDO updateOrderSettlePrices(List<OrderSettlePriceVO> orderSettlePrices) {
        this.orderSettlePrices = orderSettlePrices;
        return this;
    }

    public OrderSettlePriceVO getOrderSettlePrice(Long supplierId) {
        return Optional.ofNullable(this.orderSettlePrices).orElse(Collections.emptyList()).stream().filter(item -> Objects.equals(item.getSupplierId(), supplierId)).findFirst().orElse(null);
    }

    /**
     * 新增映射
     * 新增的司机id如果不在存量的司机集合中，则新增映射
     * @param indexes
     * @param driverIds
     * @return
     */
    public List<GrabDspOrderDriverIndexDO> newIndex(List<GrabDspOrderDriverIndexDO> indexes, List<Long> driverIds) {
        if (CollectionUtils.isEmpty(driverIds)) {
            return Collections.emptyList();
        }
        ParentCategoryEnum parentCategoryCode = CategoryCodeEnum.isCharterOrder(this.getCategoryCode()) ? ParentCategoryEnum.DAY : ParentCategoryEnum.JNT;
        Set<Long> oldDriverIds = Optional.ofNullable(indexes).orElse(Collections.emptyList()).stream().map(GrabDspOrderDriverIndexDO::getDriverId).collect(Collectors.toSet());
        return driverIds.stream()
                .filter(id -> !oldDriverIds.contains(id))
                .map(id -> new GrabDspOrderDriverIndexDO(this.getDspOrderId(), id, this.getEstimatedUseTimeBj(), this.getDuid(), this.getCityId(), parentCategoryCode))
                .toList();
    }

    /**
     * 更新映射
     * 存量的映射司机id如果不再全量的司机集合中，则设置成失效
     * @param indexes
     * @param driverIds
     * @return
     */
    public List<GrabDspOrderDriverIndexDO> updateIndex(List<GrabDspOrderDriverIndexDO> indexes, List<Long> driverIds) {
        if (CollectionUtils.isEmpty(driverIds)) {
            return Collections.emptyList();
        }
        List<GrabDspOrderDriverIndexDO> updateIndex = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(indexes)) {
            for (GrabDspOrderDriverIndexDO index : indexes) {
                if (!driverIds.contains(index.getDriverId())) {
                    // 老司机映射不在全量司机集合内，设置为失效
                    index.updateValid(YesOrNo.NO.getCode());
                }
                // 更新映射的duid
                index.setDuid(this.getDuid());
                updateIndex.add(index);
            }
        }
        return updateIndex;
    }

    public Boolean rule(List<GrabDspOrderDriverIndexDO> indexes) {
        GrabOrderPushRuleHandler handler = GrabOrderPushRuleContext.get(this.getGrabType());
        return handler.handle(this, indexes);
    }

    public GrabDspOrderSnapshotDO updateGrabPushTime(Date grabPushTimeLocal, Date grabPushTimeBj) {
        this.isUpdateGrabPushTime = Objects.equals(this.grabPushTimeLocal, grabPushTimeLocal);
        this.grabPushTimeLocal = grabPushTimeLocal;
        this.grabPushTimeBj = grabPushTimeBj;
        return this;
    }

    public boolean isBefore(Date grabPushTimeLocal) {
        return Objects.nonNull(this.grabPushTimeLocal) && Objects.nonNull(grabPushTimeLocal) && this.grabPushTimeLocal.before(grabPushTimeLocal);
    }

    public GrabDspOrderSnapshotDO taken(DspOrderVO dspOrder, List<GrabDspOrderDriverIndexDO> indexes) {
        // 更新状态
        this.orderStatus = dspOrder.getOrderStatus();
        DspType dspType = DspType.getDspType(dspOrder.getOrderConfirmRecordDuid());
        if (Objects.equals(DspType.GRAB_BROADCAST, dspType)) {
            // 抢单成功
            this.grabStatus = GrabDspOrderSnapshotStatusEnum.SUCCESS;
            this.driverId = dspOrder.getOrderConfirmRecordDriverId();
            Optional.ofNullable(indexes)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(index -> Objects.equals(index.getDriverId(), this.driverId))
                    .findFirst()
                    .ifPresent(index -> index.updateTaken(YesOrNo.YES.getCode()));
        } else {
            // 抢单取消
            this.grabStatus = GrabDspOrderSnapshotStatusEnum.CANCEL;
        }
        this.supplierId = ObjectUtils.defaultIfNull(dspOrder.getSupplierId(), 0).longValue();
        return this;
    }

    public GrabDspOrderSnapshotDO driverConfirm(DspOrderVO dspOrder) {
        // 更新状态
        this.orderStatus = dspOrder.getOrderStatus();
        // 抢单取消
        this.grabStatus = GrabDspOrderSnapshotStatusEnum.CANCEL;
        return this;
    }

    public GrabDspOrderSnapshotDO dispatcherConfirm(DspOrderVO dspOrder) {
        // 更新状态
        this.orderStatus = dspOrder.getOrderStatus();
        if (Objects.equals(this.getSupplierId(), dspOrder.getSupplierId().longValue())) {
            // 其他供应商调度确认了，抢单取消
            this.grabStatus = GrabDspOrderSnapshotStatusEnum.CANCEL;
        }
        this.supplierId = ObjectUtils.defaultIfNull(dspOrder.getSupplierId(), 0).longValue();
        return this;
    }

    public GrabDspOrderSnapshotDO cancel(List<GrabDspOrderDriverIndexDO> indexes) {
        this.setGrabStatus(GrabDspOrderSnapshotStatusEnum.CANCEL);
        this.setOrderStatus(OrderStatusEnum.ORDER_CANCEL.getCode());
        if (CollectionUtils.isNotEmpty(indexes)) {
            for (GrabDspOrderDriverIndexDO index : indexes) {
                // 司机映射设置为删除
                index.updateValid(2);
            }
        }
        return this;
    }
}
