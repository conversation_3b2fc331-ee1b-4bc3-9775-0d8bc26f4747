package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.enums.DspStage;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.DriverLeaveGateway;
import com.ctrip.dcs.domain.schedule.value.DriverLeaveVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class DriverLeaveVisitor extends AbstractVisitor {
    private static final Logger logger = LoggerFactory.getLogger(DriverLeaveVisitor.class);

    private DriverLeaveGateway driverLeaveGateway;

    private List<DriverVO> drivers;

    public DriverLeaveVisitor(List<DriverVO> drivers) {
        Assert.notNull(drivers);
        this.drivers = drivers;
        this.driverLeaveGateway = getInstance(DriverLeaveGateway.class);
    }

    public DriverLeaveVisitor(DriverLeaveGateway driverLeaveGateway, List<DriverVO> drivers) {
        this.driverLeaveGateway = driverLeaveGateway;
        this.drivers = drivers;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, List<DriverLeaveVO>> map = context.getDriverLeaveMap();
        List<Long> driverIds = getDriverIds(this.drivers, map.keySet());
        List<DriverLeaveVO> driverLeaves = getDriverLeaves(driverIds, context);
        if (CollectionUtils.isNotEmpty(driverLeaves)) {
            for (DriverLeaveVO leave : driverLeaves) {
                if (leave != null && leave.getDriverId() != null) {
                    if (!map.containsKey(leave.getDriverId())) {
                        map.put(leave.getDriverId(), Lists.newArrayList());
                    }
                    map.get(leave.getDriverId()).add(leave);
                }
            }
        }

    }

    private List<DriverLeaveVO> getDriverLeaves(List<Long> driverIds , CheckContext context){
        DspStage dspStage = context.getDspStage();
        logger.info("DriverLeaveVisitor_getDriverLeaves", JsonUtil.toJson(driverIds));
        if (Objects.equals(dspStage, DspStage.DSP)) {
            return driverLeaveGateway.queryDrvLeaveDetail(Sets.newHashSet(driverIds), true);
        }
        return driverLeaveGateway.queryDrvLeaveDetail(Sets.newHashSet(driverIds), false);
    }

}
