package com.ctrip.dcs.domain.schedule.repository;

import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

;

/**
 * <AUTHOR>
 */
public interface ScheduleTaskRepository {

    @Deprecated
    ScheduleTaskDO find(Long taskId, String dspOrderId);

    ScheduleTaskDO find(String scheduleTaskId, String dspOrderId);

    ScheduleTaskDO query(String duid);

    List<ScheduleTaskDO> query(Long scheduleId, String dspOrderId);

    void save(List<ScheduleTaskDO> task);

    void complete(ScheduleTaskDO task);

    void execute(ScheduleTaskDO task, DspOrderVO order);

    void cancel(ScheduleTaskDO task);


    void rebuild(ScheduleTaskDO task);

    Map<String, BigDecimal> batchQueryRewardByDuid(List<String> duidList);

    void updateDspRewardStrategyId(ScheduleTaskDO task, DspOrderVO order);
}
