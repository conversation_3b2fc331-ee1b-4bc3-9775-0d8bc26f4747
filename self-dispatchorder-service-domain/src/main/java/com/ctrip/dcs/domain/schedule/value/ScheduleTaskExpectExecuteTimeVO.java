package com.ctrip.dcs.domain.schedule.value;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.enums.ScheduleTaskStatus;
import com.ctrip.dcs.domain.common.util.DateUtil;
import org.apache.commons.lang.time.DateUtils;

import java.util.Date;
import java.util.function.Function;

/**
 * 调度任务预估执行时间
 * <AUTHOR>
 */
public class ScheduleTaskExpectExecuteTimeVO {

    private static final Function<ScheduleTaskExpectExecuteTimeVO, Date> initExecuteTime = (timeVO) -> {
        Date executeTime =  DateUtils.addDays(timeVO.lastConfirmTime, 90);;
        if (timeVO.begin != null && timeVO.begin >= 0L) {
            Date beginTime = DateUtil.addSeconds(timeVO.createTime, timeVO.begin);
            executeTime = DateUtil.min(executeTime, beginTime);
        }
        if (timeVO.end != null && timeVO.end >= 0L) {
            Date endTime = DateUtil.addSeconds(timeVO.lastConfirmTime, -timeVO.end);
            executeTime = DateUtil.min(executeTime, endTime);
        }
        if (timeVO.limit != null && timeVO.limit >= 0L) {
            Date limitTime = DateUtil.addSeconds(timeVO.sysExpectBookTime, -timeVO.limit);
            executeTime = DateUtil.min(executeTime, limitTime);
        }
        return executeTime;
    };

    private static final Function<ScheduleTaskExpectExecuteTimeVO, Date> retryExecuteTime = (timeVO) -> {
        if (timeVO.retry != null && timeVO.retry > 0L) {
            return DateUtil.addSeconds(timeVO.executeTime, timeVO.retry.intValue());
        }
        return null;
    };

    private ScheduleTaskStatus status;
    private Date createTime;
    private Integer begin;
    private Date lastConfirmTime;
    private Integer end;
    private Date sysExpectBookTime;
    private Integer limit;
    private Date executeTime;
    private Long retry;

    public ScheduleTaskExpectExecuteTimeVO(ScheduleTaskStatus status, Date createTime, Integer begin, Date lastConfirmTime, Integer end, Date sysExpectBookTime, Integer limit, Date executeTime, Long retry) {
        Assert.notNull(status);
        Assert.notNull(createTime);
        Assert.notNull(lastConfirmTime);
        Assert.notNull(sysExpectBookTime);
        this.status = status;
        this.createTime = createTime;
        this.begin = begin;
        this.lastConfirmTime = lastConfirmTime;
        this.end = end;
        this.sysExpectBookTime = sysExpectBookTime;
        this.limit = limit;
        this.executeTime = executeTime;
        this.retry = retry;
    }

    public Date getDate() {
        return this.status == ScheduleTaskStatus.INIT ? initExecuteTime.apply(this) : retryExecuteTime.apply(this);
    }

    public boolean after(Date date) {
        Date executeTime = getDate();
        return executeTime == null || executeTime.after(date);
    }

}
