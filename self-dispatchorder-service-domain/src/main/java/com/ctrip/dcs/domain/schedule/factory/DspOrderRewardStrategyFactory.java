package com.ctrip.dcs.domain.schedule.factory;

import com.ctrip.dcs.domain.common.enums.DspOrderRewardStrategyType;
import com.ctrip.dcs.domain.common.enums.ScheduleType;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.entity.DspOrderRewardStrategyDO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DspOrderRewardStrategyFactory {

    /**
     * 创建加价奖励策略
     * @param order
     * @return
     */
    List<DspOrderRewardStrategyDO> create(DspOrderVO order, DspOrderRewardStrategyType dspOrderRewardStrategyType);

}
