package com.ctrip.dcs.domain.schedule.service;

import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand;
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand;

import java.util.List;

;

/**
 * <AUTHOR>
 */
public interface CheckService {

    List<CheckModel> check(DspCheckCommand command);

    CheckModel check(TakenCheckCommand command);

    List<CheckModel> grabCheck(DspCheckCommand command);

}
