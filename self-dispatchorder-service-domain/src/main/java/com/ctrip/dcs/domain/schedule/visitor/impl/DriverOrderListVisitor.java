package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DriverWorkTimeVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DriverOrderListVisitor extends AbstractVisitor {

    private static final Logger logger = LoggerFactory.getLogger(DriverOrderListVisitor.class);


    private static final List<OrderStatusEnum> ORDER_STATUS_LIST = Lists.newArrayList(OrderStatusEnum.DRIVER_CONFIRMED, OrderStatusEnum.DRIVER_CAR_CONFIRMED, OrderStatusEnum.DRIVER_TO_MEET, OrderStatusEnum.DRIVER_ARRIVE, OrderStatusEnum.DRIVER_SERVICE_START, OrderStatusEnum.DRIVER_SERVICE_END, OrderStatusEnum.ORDER_FINISH);

    private DspOrderVO order;

    private List<DriverVO> drivers;

    private QueryDspOrderService queryDspOrderService;

    private String cityIds;

    public DriverOrderListVisitor(DspOrderVO order, List<DriverVO> drivers) {
        Assert.notNull(order);
        Assert.notEmpty(drivers);
        this.order = order;
        this.drivers = drivers;
        this.queryDspOrderService = getInstance("queryDspOrderCacheServiceImpl", QueryDspOrderService.class);
        this.cityIds = getInstance("commonConfConfig", ConfigService.class).getString("driverOrderListVisitor", null);
    }

    public DriverOrderListVisitor(DspOrderVO order, List<DriverVO> drivers, QueryDspOrderService queryDspOrderService) {
        this.order = order;
        this.drivers = drivers;
        this.queryDspOrderService = queryDspOrderService;
        this.cityIds = getInstance("commonConfConfig", ConfigService.class).getString("driverOrderListVisitor", null);
    }

    public DriverOrderListVisitor(DspOrderVO order, List<DriverVO> drivers, QueryDspOrderService queryDspOrderService, String cityIds) {
        this.order = order;
        this.drivers = drivers;
        this.queryDspOrderService = queryDspOrderService;
        this.cityIds = cityIds;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, List<DspOrderVO>> orderListMap = context.getContext().getOrderListMap();
        visit(orderListMap);
    }

    @Override
    public void visit(SortContext context) {
        Map<Long, List<DspOrderVO>> orderListMap = context.getDspContext().getOrderListMap();
        visit(orderListMap);
    }

    private void visit(Map<Long, List<DspOrderVO>> orderListMap) {
        List<DriverVO> filter = filter(drivers, orderListMap.keySet());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        Map<Long, List<DspOrderVO>> result = queryOrderList(order, filter);
        orderListMap.putAll(result);
    }

    private boolean grayFlag(DspOrderVO order, String cityIds){
        if(StringUtils.isBlank(cityIds)){
            return false;
        }
        List<String> list = Arrays.stream(cityIds.split(",")).toList();
        return list.contains("0") || list.contains(order.getCityId().toString());
    }

    public Map<Long, List<DspOrderVO>> queryOrderList(DspOrderVO dspOrder, List<DriverVO> drivers) {
        Map<Long, DriverWorkTimeVO> workTimeMap = Maps.newHashMap();
        List<Long> driverIds = Lists.newArrayList();
        Date begin = null;
        Date end = null;
        for (DriverVO driver : drivers) {
            DriverWorkTimeVO workTime = driver.findHitWorkTime(dspOrder);
            if (Objects.isNull(workTime)) {
                continue;
            }
            workTimeMap.put(driver.getDriverId(), workTime);
            driverIds.add(driver.getDriverId());
            if (begin == null || workTime.getStart().before(begin)) {
                begin = workTime.getStart();
            }
            if (end == null || workTime.getEnd().after(end)) {
                end = workTime.getEnd();
            }
        }
        if (CollectionUtils.isEmpty(drivers) || Objects.isNull(begin) || Objects.isNull(end)) {
            return Collections.emptyMap();
        }
        boolean grayFlag = grayFlag(dspOrder, cityIds);
        logger.info("DriverOrderListVisitor_queryOrderList1", "begin: {}, end: {}, b: {}",  begin, end, grayFlag);
        if(grayFlag){
            begin = DateUtil.addHours(begin, -2);
            end = DateUtil.addHours(end, 2);
        }
        logger.info("DriverOrderListVisitor_queryOrderList2", "begin: {}, end: {}, b: {}",  begin, end, grayFlag);
        Map<Long, List<DspOrderVO>> orderMap = queryDspOrderService.queryOrderList(drivers, begin, end, ORDER_STATUS_LIST);
        if (MapUtils.isEmpty(orderMap)) {
            return Collections.emptyMap();
        }
        Map<Long, List<DspOrderVO>> result = Maps.newHashMap();
        orderMap.forEach((driverId, orders) -> {
            DriverWorkTimeVO driverWorkTime = workTimeMap.get(driverId);
            if (Objects.isNull(driverWorkTime)) {
                return;
            }
            List<DspOrderVO> list = orders.stream()
                    .filter(o -> driverWorkTime.contain(o.getEstimatedUseTime(), grayFlag))
                    .collect(Collectors.toList());
            result.put(driverId, list);
        });
        return result;
    }
}
