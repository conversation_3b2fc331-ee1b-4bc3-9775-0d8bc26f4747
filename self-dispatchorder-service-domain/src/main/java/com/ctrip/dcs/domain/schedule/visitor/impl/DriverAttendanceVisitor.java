package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.repository.DriverAttendanceRepository;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.value.DriverAttendanceVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DriverAttendanceVisitor extends AbstractVisitor {

    private static final Logger logger = LoggerFactory.getLogger(DriverAttendanceVisitor.class);


    private List<DriverVO> drivers;

    private QueryDspOrderService queryDspOrderService;

    private DriverAttendanceRepository driverAttendanceRepository;

    private String querySource;


    public DriverAttendanceVisitor(List<DriverVO> drivers) {
        Assert.notEmpty(drivers);
        this.drivers = drivers;
        this.queryDspOrderService = getInstance("queryDspOrderCacheServiceImpl", QueryDspOrderService.class);
        this.driverAttendanceRepository = getInstance("driverAttendanceRepositoryImpl", DriverAttendanceRepository.class);
        this.querySource = getInstance("commonConfConfig", ConfigService.class).getString("driverAttendanceVisitor", null);
    }

    public DriverAttendanceVisitor(List<DriverVO> drivers, QueryDspOrderService queryDspOrderService, DriverAttendanceRepository driverAttendanceRepository, String querySource) {
        this.drivers = drivers;
        this.queryDspOrderService = queryDspOrderService;
        this.driverAttendanceRepository = driverAttendanceRepository;
        this.querySource = querySource;
    }


    @Override
    public void visit(CheckContext context) {
    }

    @Override
    public void visit(SortContext context) {
        Map<Long, DriverAttendanceVO> attendanceMap = context.getDriverAttendanceMap();
        visit(attendanceMap);
    }

    private void visit(Map<Long, DriverAttendanceVO> attendanceMap) {
        List<DriverVO> filter = filter(drivers, attendanceMap.keySet());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        Map<Long, DriverAttendanceVO> result = queryOrderList(filter);
        attendanceMap.putAll(result);
    }

    public Map<Long, DriverAttendanceVO> queryOrderList(List<DriverVO> drivers) {
        logger.info("DriverAttendanceVisitor", querySource);
        if(StringUtils.isNotBlank(querySource)){
            Map<Long, DriverAttendanceVO> longDriverAttendanceVOMap = queryOrderListFromRedis(drivers);
            if(longDriverAttendanceVOMap != null){
                return longDriverAttendanceVOMap;
            }
        }
        Date minMonthDate = DateUtil.getMinMonthDate();
        if (CollectionUtils.isEmpty(drivers)) {
            return Collections.emptyMap();
        }
        Map<Long, List<DspOrderVO>> orderMap = queryDspOrderService.queryOrderList(drivers, minMonthDate, DateUtil.nextDay(), Lists.newArrayList(OrderStatusEnum.ORDER_FINISH, OrderStatusEnum.DRIVER_SERVICE_END));
        if (MapUtils.isEmpty(orderMap)) {
            return Collections.emptyMap();
        }
        Map<Long, DriverAttendanceVO> result = Maps.newHashMap();
        for (Map.Entry<Long, List<DspOrderVO>> entry : orderMap.entrySet()) {
            Long drvId = entry.getKey();
            List<DspOrderVO> orderList = entry.getValue();
            DriverAttendanceVO attendanceVO = new DriverAttendanceVO();
            attendanceVO.setDriverId(drvId);
            attendanceVO.setAttendance(0.0D);
            if (CollectionUtils.isNotEmpty(orderList)) {
                Set<String> set = orderList.stream().map(s -> DateUtil.formatDate(s.getEstimatedUseTime(), DateUtil.DATE_FORMAT)).collect(Collectors.toSet());
                attendanceVO.setAttendance((double) set.size());
            }
            result.put(drvId, attendanceVO);
        }

        return result;
    }


    public Map<Long, DriverAttendanceVO> queryOrderListFromRedis(List<DriverVO> drivers) {
        try{
            if (CollectionUtils.isEmpty(drivers)) {
                return Collections.emptyMap();
            }
            //加缓存 从缓存中查询
            List<String> collect = drivers.stream().map(x -> x.getDriverId().toString()).toList();
            Map<String, Integer> stringIntegerMap = driverAttendanceRepository.queryDriverAttendance(collect, new Date());
            Map<Long, DriverAttendanceVO> result = Maps.newHashMap();
            for (Map.Entry<String, Integer> entry : stringIntegerMap.entrySet()) {
                Long key = Long.valueOf(entry.getKey());
                Integer value = entry.getValue();
                DriverAttendanceVO attendanceVO = new DriverAttendanceVO();
                attendanceVO.setDriverId(key);
                attendanceVO.setAttendance(value.doubleValue());
                result.put(key, attendanceVO);
            }
            return result;
        }catch (Exception ex){
            logger.error("DriverAttendanceVisitor", ex);
            return null;
        }

    }
}
