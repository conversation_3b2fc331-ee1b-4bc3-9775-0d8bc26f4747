package com.ctrip.dcs.domain.schedule.factory;

import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DispatcherGrabOrderFactory {

    List<DispatcherGrabOrderDO> create(ScheduleTaskDO task, DspOrderVO order, List<TransportGroupVO> transportGroups);

}
