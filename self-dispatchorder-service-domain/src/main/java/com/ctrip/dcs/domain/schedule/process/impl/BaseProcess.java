package com.ctrip.dcs.domain.schedule.process.impl;

import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.BatchConfirmDspOrderVO;
import com.ctrip.dcs.domain.common.value.DriverOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.common.value.graborder.GrabOrderDTO;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.CompleteTaskEvent;
import com.ctrip.dcs.domain.schedule.event.DriverOrderConfirmFailEvent;
import com.ctrip.dcs.domain.schedule.event.ExpireGrabOrderEvent;
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.service.RecommendService;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class BaseProcess {

    private static final Logger logger = LoggerFactory.getLogger(BaseProcess.class);

    @Autowired
    protected RecommendService recommendService;

    @Autowired
    private CheckService checkService;

    @Autowired
    protected ScheduleTaskRepository taskRepository;

    @Autowired
    protected DriverOrderFactory driverOrderFactory;

    @Autowired
    protected MessageProviderService messageProducer;

    @Autowired
    protected SelfOrderQueryGateway selfOrderQueryGateway;

    @Autowired
    @Qualifier("queryGrabDriverListThreadPool")
    protected ExecutorService queryGrabDriverListThreadPool;

    @Autowired
    protected GrabOrderDetailRepository grabOrderDetailRepository;

    protected void completeTask(ScheduleTaskDO task, Boolean confirm) {
        // 任务完成
        task.complete();
        taskRepository.complete(task);
        // 发送任务完成消息
        messageProducer.send(new CompleteTaskEvent(task.getDspOrderId(), task.getScheduleId(), task.getTaskId(), task.getScheduleTaskId(), YesOrNo.valueOf(confirm).getCode()));
    }

    /**
     * 抢单失效消息
     * @param orders
     */
    protected void sendGrabOrderExpireMessage(List<GrabOrderDO> orders) {
        logger.info("BaseProcess_sendGrabOrderExpireMessage_enter", JsonUtils.toJson(orders));
        if(CollectionUtils.isEmpty(orders)){
            return;
        }
        Set<Long> driverIds = orders.stream().map(GrabOrderDO::getDriverId).collect(Collectors.toSet());
        logger.info("BaseProcess_sendGrabOrderExpireMessage_driverIds", JsonUtils.toJson(driverIds));
        GrabOrderDO grabOrderDO = orders.get(0);
        long delay = grabOrderDO.getExpire() - System.currentTimeMillis();
        String driverIdsStr = StringUtils.join(driverIds, ",");
        messageProducer.send(new ExpireGrabOrderEvent(grabOrderDO.getDuid(), grabOrderDO.getDspOrderId(), grabOrderDO.getDriverId(), grabOrderDO.getExpire(), delay, driverIdsStr));
    }

    protected void sendDriverOrderConfirmFailMessage(DriverOrderVO driverOrder) {
        if (Objects.isNull(driverOrder) || Objects.equals(driverOrder.getDspOrderId(), driverOrder.getDriverOrderId())) {
            return;
        }
        messageProducer.send(new DriverOrderConfirmFailEvent(driverOrder.getDriverOrderId(), driverOrder.getDspOrderId(), driverOrder.getDriverId()));
    }

    protected void sendDriverOrderConfirmFailMessage(BatchConfirmDspOrderVO group, DriverVO driver) {
        if (Objects.isNull(group) || CollectionUtils.isEmpty(group.getDetails())) {
            return;
        }
        for (BatchConfirmDspOrderVO.ConfirmDspOrderDetailVO detail : group.getDetails()) {
            messageProducer.send(new DriverOrderConfirmFailEvent(detail.getDriverOrderId(), detail.getDspOrderId(), driver.getDriverId()));
        }
    }

    protected void saveGrabOrderDetail(String dspOrderId, List<DriverVO> drivers, SubSkuVO subSku, DuidVO duid, List<GrabOrderDO> grabOrders) {
        try{
            //抢单大厅和播报 详情信息
            //优化，根绝司机id和派发单id，获取全量抢单数据
            List<GrabOrderDTO> grabOrderDTOS = queryGrabDriverList(dspOrderId, drivers, subSku, duid);
            logger.info("BaseProcess_saveGrabOrderDetail", JsonUtils.toJson(grabOrderDTOS));

            if(CollectionUtils.isNotEmpty(grabOrderDTOS)){
                logger.info("BaseProcess_saveGrabOrderDetail_grabOrderDTOMap", JsonUtils.toJson(grabOrders));
                grabOrderDetailRepository.saveFullData(grabOrderDTOS);
                grabOrderDetailRepository.save(grabOrders);
            }else{
                MetricsUtil.recordValue("BaseProcess_queryGrabDriverList_empty", 1);
            }
        }catch (Exception ex){
            MetricsUtil.recordValue("BaseProcess_saveGrabOrderDetail_error", 1);
            logger.info("BaseProcess_saveGrabOrderDetail_error", ex);
        }
    }

    private List<GrabOrderDTO> queryGrabDriverList(String dspOrderId, List<DriverVO> drivers, SubSkuVO subSku, DuidVO duid) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("drivers", JsonUtil.toJson(drivers));
            map.put("subSku", JsonUtil.toJson(subSku));
            logger.info("BaseProcess_queryGrabDriverList_enter", JsonUtils.toJson(map));
            List<Long> driverIds = drivers.stream().map(DriverVO::getDriverId).collect(Collectors.toList());
            List<GrabOrderDTO> grabOrderDTOS = selfOrderQueryGateway.queryGrabDriverList(dspOrderId, duid.toString(), new HashSet<>(driverIds));
            logger.info("BaseProcess_queryGrabDriverList", JsonUtils.toJson(grabOrderDTOS));
            return grabOrderDTOS;
        } catch (Exception e) {
            logger.error("BaseProcess_queryGrabDriverList_error", e);
        }
        return Lists.newArrayList();
    }

    protected void upLoadMeMetrics(String name, DuidVO duid, List<DspModelVO> list) {
        try{
            MetricsUtil.recordValue("dsp_type_" + duid.getDspType() + "_" + name, list.size());
        }catch (Exception ex){
            logger.error("upLoadMeMetrics_error", ex);
        }
    }
}
