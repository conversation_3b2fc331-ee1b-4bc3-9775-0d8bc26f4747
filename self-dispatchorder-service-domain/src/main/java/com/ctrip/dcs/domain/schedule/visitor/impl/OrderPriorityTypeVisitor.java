package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.service.OrderFeePriorityService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.check.value.OrderPriorityType;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class OrderPriorityTypeVisitor extends AbstractVisitor {

    private DspOrderVO dspOrder;

    private OrderFeePriorityService orderFeePriorityService;

    public OrderPriorityTypeVisitor(DspOrderVO dspOrder) {
        Assert.notNull(dspOrder);
        this.dspOrder = dspOrder;
        this.orderFeePriorityService = getInstance(OrderFeePriorityService.class);
    }

    public OrderPriorityTypeVisitor(DspOrderVO dspOrder, OrderFeePriorityService orderFeePriorityService) {
        this.dspOrder = dspOrder;
        this.orderFeePriorityService = orderFeePriorityService;
    }

    @Override
    public void visit(CheckContext context) {
        Map<String, OrderPriorityType> map = context.getOrderPriorityTypeMap();
        if (map.containsKey(dspOrder.getDspOrderId())) {
            return;
        }
        OrderPriorityType priorityType = orderFeePriorityService.queryOrderPriorityType(dspOrder);
        map.put(dspOrder.getDspOrderId(), priorityType);
    }
}
