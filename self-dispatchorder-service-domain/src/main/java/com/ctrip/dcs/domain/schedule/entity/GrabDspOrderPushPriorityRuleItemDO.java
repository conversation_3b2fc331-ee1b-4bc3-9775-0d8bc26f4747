package com.ctrip.dcs.domain.schedule.entity;

import cn.hutool.core.lang.Pair;

import java.util.List;

/**
 * <AUTHOR>
 */
public class GrabDspOrderPushPriorityRuleItemDO {

    private Integer index;

    private List<Pair<String, String>> data;

    private Long delay;

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public List<Pair<String, String>> getData() {
        return data;
    }

    public void setData(List<Pair<String, String>> data) {
        this.data = data;
    }

    public Long getDelay() {
        return delay;
    }

    public void setDelay(Long delay) {
        this.delay = delay;
    }
}
