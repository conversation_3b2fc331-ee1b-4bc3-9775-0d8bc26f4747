package com.ctrip.dcs.domain.schedule.visitor.impl;

import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;
import com.ctrip.dcs.domain.schedule.repository.SelectGrabOrderRepository;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DriverGrabVisitor extends AbstractVisitor {

    private SelectGrabOrderRepository selectGrabOrderRepository;

    public DriverGrabVisitor() {
        this.selectGrabOrderRepository = getInstance(SelectGrabOrderRepository.class);
    }

    public DriverGrabVisitor(SelectGrabOrderRepository selectGrabOrderRepository) {
        this.selectGrabOrderRepository = selectGrabOrderRepository;
    }

    @Override
    public void visit(SortContext context) {
        List<GrabOrderDO> list = queryGrabOrder(context.getDspOrder(), context.getSubSku());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (GrabOrderDO order : list) {
            context.getDriverGrabOrderMap().put(order.getDriverId(), order);
        }
    }

    public List<GrabOrderDO> queryGrabOrder(DspOrderVO dspOrder, SubSkuVO skuVO) {
        if (!DspType.isBroadcast(skuVO.getDspType().getCode()) && !DspType.isGrabCentre(skuVO.getDspType().getCode())) {
            return Collections.emptyList();
        }
        return selectGrabOrderRepository.find(dspOrder.getDspOrderId(), skuVO.getSubSkuId());
    }
}
