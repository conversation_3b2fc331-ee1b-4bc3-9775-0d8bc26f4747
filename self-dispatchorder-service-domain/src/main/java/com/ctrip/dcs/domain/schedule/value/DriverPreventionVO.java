package com.ctrip.dcs.domain.schedule.value;

/**
 * <AUTHOR>
 */
public class DriverPreventionVO {

    private Long driverId;

    /**
     * 要求报告状态
     * 0： 无需上传核酸报告 & 疫苗报告
     * <p>
     * 1：必须上传核酸报告
     * <p>
     * 2：必须上传疫苗报告
     * <p>
     * 3：满足 上传核酸报告 || 疫苗报告 二选一即可
     * <p>
     * 4：必须上传核酸报告 & 疫苗报告
     */
    private Integer constraintReportStatus;

    /**
     * 上传核酸证明天数
     * 从核酸证明生效起至现在的天数 自然小时数
     */
    private Integer nucleicAcidUploadDays;

    /**
     * 接种次数
     * 0 <= x <= 2
     */
    private Integer vaccinationCount;

    /**
     * 核酸上传结果
     * 0-未上传，1-已上传
     */
    private Integer nucleicAcidTestingResult;

    /**
     * 疫苗上传结果
     * 0-未上传，1-已上传
     */
    private Integer vaccinationResult;

    /**
     * 核酸报告状态
     * 核酸失效-false,核酸正常-true
     */
    private Boolean nucleicAcidTestingStatus;

    /**
     * 疫苗接种状态
     * 不管打一针还是两针 ，只要接种了既是true，反之为false
     */
    private Boolean vaccinationStatus;

    /**
     * 核酸过期时间  YYYY-MM-DD HH:mm:ss
     */
    private String nucleicAcidExpirationDate;


    public Long getDriverId() {
        return driverId;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Integer getConstraintReportStatus() {
        return constraintReportStatus;
    }

    public void setConstraintReportStatus(Integer constraintReportStatus) {
        this.constraintReportStatus = constraintReportStatus;
    }

    public Integer getNucleicAcidUploadDays() {
        return nucleicAcidUploadDays;
    }

    public void setNucleicAcidUploadDays(Integer nucleicAcidUploadDays) {
        this.nucleicAcidUploadDays = nucleicAcidUploadDays;
    }

    public Integer getVaccinationCount() {
        return vaccinationCount;
    }

    public void setVaccinationCount(Integer vaccinationCount) {
        this.vaccinationCount = vaccinationCount;
    }

    public Integer getNucleicAcidTestingResult() {
        return nucleicAcidTestingResult;
    }

    public void setNucleicAcidTestingResult(Integer nucleicAcidTestingResult) {
        this.nucleicAcidTestingResult = nucleicAcidTestingResult;
    }

    public Boolean getVaccinationStatus() {
        return vaccinationStatus;
    }

    public void setVaccinationStatus(Boolean vaccinationStatus) {
        this.vaccinationStatus = vaccinationStatus;
    }

    public Integer getVaccinationResult() {
        return vaccinationResult;
    }

    public void setVaccinationResult(Integer vaccinationResult) {
        this.vaccinationResult = vaccinationResult;
    }

    public Boolean getNucleicAcidTestingStatus() {
        return nucleicAcidTestingStatus;
    }

    public void setNucleicAcidTestingStatus(Boolean nucleicAcidTestingStatus) {
        this.nucleicAcidTestingStatus = nucleicAcidTestingStatus;
    }

    public String getNucleicAcidExpirationDate() {
        return nucleicAcidExpirationDate;
    }

    public void setNucleicAcidExpirationDate(String nucleicAcidExpirationDate) {
        this.nucleicAcidExpirationDate = nucleicAcidExpirationDate;
    }
}
