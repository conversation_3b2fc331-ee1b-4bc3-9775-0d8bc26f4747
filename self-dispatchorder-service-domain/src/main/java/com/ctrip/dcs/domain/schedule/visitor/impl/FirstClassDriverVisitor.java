package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.FirstClassDriverGateway;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
public class FirstClassDriverVisitor extends AbstractVisitor {

    private DspOrderVO dspOrder;

    private FirstClassDriverGateway firstClassDriverGateway;

    public FirstClassDriverVisitor(DspOrderVO dspOrder) {
        Assert.notNull(dspOrder);
        this.dspOrder = dspOrder;
        this.firstClassDriverGateway = getInstance(FirstClassDriverGateway.class);
    }

    public FirstClassDriverVisitor(DspOrderVO dspOrder, FirstClassDriverGateway firstClassDriverGateway) {
        this.dspOrder = dspOrder;
        this.firstClassDriverGateway = firstClassDriverGateway;
    }

    @Override
    public void visit(CheckContext context) {
        List<Long> driverIds = context.getFirstClassDriverList();
        if (CollectionUtils.isNotEmpty(driverIds)) {
            return;
        }
        List<Long> list = firstClassDriverGateway.queryFirstClassDriver(dspOrder.getCityId(), dspOrder.getCarTypeId(), dspOrder.getEstimatedUseTime());
        driverIds.addAll(list);
    }
}
