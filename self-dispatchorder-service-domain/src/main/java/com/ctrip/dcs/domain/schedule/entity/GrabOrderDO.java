package com.ctrip.dcs.domain.schedule.entity;

import cn.hutool.core.lang.Assert;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public class GrabOrderDO {

    private String duid;

    private String dspOrderId;

    private String userOrderId;

    private String driverOrderId;

    private Integer subSkuId;

    private Long driverId;

    private Long useTime;

    private Long submitTime;

    private Long expire;

    private Integer premium;

    private GrabOrderDO subGrabOrder;

    @JsonCreator
    public GrabOrderDO(@JsonProperty("duid") String duid, @JsonProperty("dspOrderId") String dspOrderId, @JsonProperty("userOrderId") String userOrderId,@JsonProperty("subSkuId") Integer subSkuId, @JsonProperty("driverId") Long driverId, @JsonProperty("useTime") Long useTime, @JsonProperty("expire") Long expire, @JsonProperty("premium") Integer premium) {
        Assert.notBlank(duid);
        Assert.notBlank(dspOrderId);
        Assert.notBlank(userOrderId);
        Assert.notNull(subSkuId);
        Assert.notNull(driverId);
        Assert.notNull(useTime);
        Assert.notNull(expire);
        this.duid = duid;
        this.dspOrderId = dspOrderId;
        this.userOrderId = userOrderId;
        this.subSkuId = subSkuId;
        this.driverId = driverId;
        this.useTime = useTime;
        this.expire = expire;
        this.premium = premium;
    }

    public static String toSelectKey(String dspOrderId, Integer subSkuId) {
        return "SUBMIT_SELECT_GRAB_ORDER#" + dspOrderId + "#" + subSkuId;
    }

    /**
     * 设置组合抢单的子单
     * @param grabOrder
     */
    public void combine(GrabOrderDO grabOrder) {
        Assert.notNull(grabOrder);
        Cat.logEvent("grabOrderDO.subGrabOrder.combine","1");
        this.subGrabOrder = grabOrder;
    }

    public void setDriverOrderId(String driverOrderId) {
        Assert.notBlank(driverOrderId);
        this.driverOrderId = driverOrderId;
    }

    /**
     * 组合抢单
     * @return
     */
    public boolean isGroup() {
        return Objects.nonNull(subGrabOrder);
    }

    public void submit() {
        this.submitTime = System.currentTimeMillis();
        if (subGrabOrder != null) {
            subGrabOrder.submit();
        }
    }
}
