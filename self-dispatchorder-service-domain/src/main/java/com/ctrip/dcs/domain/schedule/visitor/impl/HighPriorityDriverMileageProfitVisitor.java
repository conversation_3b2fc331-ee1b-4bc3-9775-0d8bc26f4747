package com.ctrip.dcs.domain.schedule.visitor.impl;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.service.QueryDriverMileageProfitService;
import com.ctrip.dcs.domain.common.value.DriverMileageProfitVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.context.DspContext;
import com.ctrip.dcs.domain.schedule.gateway.HighPriorityDriverGateway;
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class HighPriorityDriverMileageProfitVisitor extends AbstractVisitor {

    private DspOrderVO dspOrder;

    private List<DriverVO> drivers;

    private DriverPointsVisitor driverPointsVisitor;

    private HighPriorityDriverGateway highPriorityDriverGateway;

    private QueryDriverMileageProfitService queryDriverMileageProfitService;

    public HighPriorityDriverMileageProfitVisitor(DspOrderVO dspOrder, List<DriverVO> drivers) {
        Assert.notNull(dspOrder);
        Assert.notEmpty(drivers);
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.driverPointsVisitor = new DriverPointsVisitor(drivers);
        this.highPriorityDriverGateway = getInstance(HighPriorityDriverGateway.class);
        this.queryDriverMileageProfitService = getInstance(QueryDriverMileageProfitService.class);
    }

    public HighPriorityDriverMileageProfitVisitor(DspOrderVO dspOrder, List<DriverVO> drivers, DriverPointsVisitor driverPointsVisitor, HighPriorityDriverGateway highPriorityDriverGateway, QueryDriverMileageProfitService queryDriverMileageProfitService) {
        this.dspOrder = dspOrder;
        this.drivers = drivers;
        this.driverPointsVisitor = driverPointsVisitor;
        this.highPriorityDriverGateway = highPriorityDriverGateway;
        this.queryDriverMileageProfitService = queryDriverMileageProfitService;
    }

    @Override
    public void visit(CheckContext context) {
        Map<Long, DriverMileageProfitVO> map = context.getHighPriorityDriverMileageProfitMap();
        List<DriverVO> filter = filter(this.drivers, map.keySet());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        driverPointsVisitor.visit(context);
        Map<Long, DriverMileageProfitVO> result = queryHighPriorityDriverProfit(filter, dspOrder,context.getContext());
        map.putAll(result);
    }

    public Map<Long, DriverMileageProfitVO> queryHighPriorityDriverProfit(List<DriverVO> drivers, DspOrderVO dspOrder,DspContext dspContext) {
        //获取头部司机列表
        List<DriverVO> highPriorityDriverVos;
        Map<Long, DriverPointsVO> driverPointsMap = dspContext.getDriverPointsMap();
        if(MapUtils.isEmpty(driverPointsMap)){
            highPriorityDriverVos = drivers;
        }else {
            List<Long> highPriorityDriverIds = highPriorityDriverGateway.queryHighPriorityDriver(drivers,dspOrder.getCityId(),driverPointsMap);
            if (CollectionUtils.isEmpty(highPriorityDriverIds)) {
                return Maps.newHashMap();
            }
            highPriorityDriverVos = drivers.stream()
                    .filter(driverVO -> highPriorityDriverIds.contains(driverVO.getDriverId()))
                    .collect(Collectors.toList());
        }

        List<DriverMileageProfitVO> driverMileageProfitVOList = queryDriverMileageProfitService.queryTodayDriverMileageProfit(highPriorityDriverVos, dspOrder.getEstimatedUseTime());
        if (CollectionUtils.isEmpty(driverMileageProfitVOList)) {
            return highPriorityDriverVos.stream()
                    .collect(Collectors.toMap(DriverVO::getDriverId,value -> DriverMileageProfitVO.builder().driverId(0L).orderCounts(0).emptyMileage(0D).income(0D).orderMileage(0D).profit(0D).highPriorityOrderCounts(0).mediumPriorityOrderCounts(0).build(),(v1, v2) -> v1));
        }
        return driverMileageProfitVOList.stream().collect(Collectors.toMap(DriverMileageProfitVO::getDriverId, item -> item,(item1,item2)->item2));
    }
}
