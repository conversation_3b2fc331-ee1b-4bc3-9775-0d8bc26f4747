package com.ctrip.dcs.infrastructure.factory;

import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.schedule.sort.Sorter;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.HierarchicalSortFactory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.HierarchicalSorter;
import com.ctrip.dcs.domain.schedule.sort.score.impl.WeightScorer;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 分层排序工厂实现类
 * 
 * 集成新的分层排序系统到现有基础设施中：
 * 1. 实现Sorter接口，保持与现有系统的兼容性
 * 2. 提供新旧系统的切换逻辑
 * 3. 实现降级和监控机制
 * 4. 管理配置和生命周期
 * 
 * <AUTHOR>
 */
@Component("hierarchicalSortFactory")
public class HierarchicalSortFactoryImpl implements Sorter {

    private static final Logger logger = LoggerFactory.getLogger(HierarchicalSortFactoryImpl.class);

    @Autowired
    private ConfigService configService;

    @Autowired
    private WeightScorer weightScorer; // 原有的权重评分器，用于降级

    private volatile HierarchicalSortFactory hierarchicalFactory;
    private volatile boolean initialized = false;

    /**
     * 实现Sorter接口的排序方法
     * 
     * @param models 待排序的司机模型列表
     * @param context 排序上下文
     * @return 排序后的司机模型列表
     */
    @Override
    public List<SortModel> sort(List<SortModel> models, SortContext context) {
        try {
            // 检查是否启用分层排序
            if (isHierarchicalSortEnabled()) {
                return performHierarchicalSort(models, context);
            } else {
                return performLegacySort(models, context);
            }
        } catch (Exception e) {
            logger.error("Sorting failed, falling back to legacy sort", e);
            return performLegacySort(models, context);
        }
    }

    /**
     * 执行分层排序
     */
    private List<SortModel> performHierarchicalSort(List<SortModel> models, SortContext context) {
        try {
            // 确保工厂已初始化
            ensureInitialized();
            
            // 获取分层排序器
            HierarchicalSorter sorter = hierarchicalFactory.getHierarchicalSorter();
            
            // 检查系统健康度
            double healthScore = hierarchicalFactory.getSystemHealthScore();
            if (healthScore < getFallbackThreshold()) {
                logger.warn("System health score {} below threshold {}, falling back to legacy sort", 
                           healthScore, getFallbackThreshold());
                return performLegacySort(models, context);
            }
            
            // 执行分层排序
            long startTime = System.currentTimeMillis();
            List<SortModel> result = sorter.sort(models, context);
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录性能指标
            recordSortingMetrics("hierarchical", models.size(), duration, true);
            
            logger.debug("Hierarchical sorting completed for {} models in {}ms", 
                        models.size(), duration);
            
            return result;
            
        } catch (Exception e) {
            logger.error("Hierarchical sorting failed", e);
            recordSortingMetrics("hierarchical", models.size(), 0, false);
            throw e; // 重新抛出异常，由上层处理降级
        }
    }

    /**
     * 执行传统排序 (降级方案)
     */
    private List<SortModel> performLegacySort(List<SortModel> models, SortContext context) {
        try {
            long startTime = System.currentTimeMillis();
            
            // 使用原有的权重评分器进行排序
            List<SortModel> result = weightScorer.sort(models, context);
            
            long duration = System.currentTimeMillis() - startTime;
            recordSortingMetrics("legacy", models.size(), duration, true);
            
            logger.debug("Legacy sorting completed for {} models in {}ms", 
                        models.size(), duration);
            
            return result;
            
        } catch (Exception e) {
            logger.error("Legacy sorting also failed", e);
            recordSortingMetrics("legacy", models.size(), 0, false);
            
            // 最后的降级方案：按司机ID排序
            return performBasicSort(models);
        }
    }

    /**
     * 基础排序 (最后的降级方案)
     */
    private List<SortModel> performBasicSort(List<SortModel> models) {
        try {
            // 简单按司机ID排序，确保结果的确定性
            models.sort((m1, m2) -> 
                Long.compare(m1.getModel().getDriver().getDriverId(), 
                           m2.getModel().getDriver().getDriverId()));
            
            logger.warn("Using basic sorting as final fallback for {} models", models.size());
            recordSortingMetrics("basic", models.size(), 0, true);
            
            return models;
            
        } catch (Exception e) {
            logger.error("Even basic sorting failed", e);
            recordSortingMetrics("basic", models.size(), 0, false);
            return models; // 返回原始列表
        }
    }

    /**
     * 检查是否启用分层排序
     */
    private boolean isHierarchicalSortEnabled() {
        try {
            return configService.getBoolean("hierarchical.sort.enabled", false);
        } catch (Exception e) {
            logger.error("Failed to check hierarchical sort enabled status", e);
            return false; // 默认禁用
        }
    }

    /**
     * 获取降级阈值
     */
    private double getFallbackThreshold() {
        try {
            return configService.getDouble("hierarchical.sort.fallback_threshold", 10.0);
        } catch (Exception e) {
            logger.error("Failed to get fallback threshold", e);
            return 10.0; // 默认阈值
        }
    }

    /**
     * 确保分层排序工厂已初始化
     */
    private void ensureInitialized() {
        if (!initialized) {
            synchronized (this) {
                if (!initialized) {
                    try {
                        hierarchicalFactory = new HierarchicalSortFactory(configService);
                        initialized = true;
                        logger.info("HierarchicalSortFactory initialized successfully");
                    } catch (Exception e) {
                        logger.error("Failed to initialize HierarchicalSortFactory", e);
                        throw new RuntimeException("Failed to initialize hierarchical sorting", e);
                    }
                }
            }
        }
    }

    /**
     * 记录排序指标
     */
    private void recordSortingMetrics(String sortType, int modelCount, long duration, boolean success) {
        try {
            // 这里可以集成到现有的监控系统中
            // 例如：发送到监控平台、记录到数据库等
            
            Map<String, Object> metrics = Map.of(
                "sort_type", sortType,
                "model_count", modelCount,
                "duration_ms", duration,
                "success", success,
                "timestamp", System.currentTimeMillis()
            );
            
            // 发送到监控系统 (示例)
            // monitoringService.recordMetrics("driver_sorting", metrics);
            
            logger.debug("Recorded sorting metrics: {}", metrics);
            
        } catch (Exception e) {
            logger.error("Failed to record sorting metrics", e);
        }
    }

    // ========== 管理接口 ==========

    /**
     * 获取系统状态信息
     */
    public Map<String, Object> getSystemStatus() {
        try {
            if (hierarchicalFactory != null) {
                return hierarchicalFactory.performDiagnostics();
            } else {
                return Map.of(
                    "hierarchical_sort_enabled", false,
                    "factory_initialized", false,
                    "status", "NOT_INITIALIZED"
                );
            }
        } catch (Exception e) {
            logger.error("Failed to get system status", e);
            return Map.of(
                "status", "ERROR",
                "error_message", e.getMessage()
            );
        }
    }

    /**
     * 获取监控统计信息
     */
    public Map<String, Object> getMonitoringStats() {
        try {
            if (hierarchicalFactory != null) {
                return hierarchicalFactory.getMonitoringStats();
            } else {
                return Map.of("status", "factory_not_initialized");
            }
        } catch (Exception e) {
            logger.error("Failed to get monitoring stats", e);
            return Map.of("error", e.getMessage());
        }
    }

    /**
     * 重新加载配置
     */
    public void reloadConfiguration() {
        try {
            if (hierarchicalFactory != null) {
                hierarchicalFactory.reloadConfig();
                logger.info("Hierarchical sort configuration reloaded");
            } else {
                logger.warn("Cannot reload configuration: factory not initialized");
            }
        } catch (Exception e) {
            logger.error("Failed to reload configuration", e);
            throw new RuntimeException("Failed to reload configuration", e);
        }
    }

    /**
     * 重置监控计数器
     */
    public void resetMonitoringCounters() {
        try {
            if (hierarchicalFactory != null) {
                hierarchicalFactory.resetMonitoringCounters();
                logger.info("Monitoring counters reset");
            }
        } catch (Exception e) {
            logger.error("Failed to reset monitoring counters", e);
        }
    }

    /**
     * 强制启用/禁用分层排序 (用于紧急情况)
     */
    public void setHierarchicalSortEnabled(boolean enabled) {
        try {
            // 这里可以通过配置服务动态修改配置
            // configService.setBoolean("hierarchical.sort.enabled", enabled);
            
            logger.info("Hierarchical sort {} by admin", enabled ? "enabled" : "disabled");
            
        } catch (Exception e) {
            logger.error("Failed to set hierarchical sort enabled status", e);
            throw new RuntimeException("Failed to update configuration", e);
        }
    }

    /**
     * 执行系统健康检查
     */
    public boolean performHealthCheck() {
        try {
            if (!isHierarchicalSortEnabled()) {
                return true; // 如果未启用，认为是健康的
            }
            
            ensureInitialized();
            double healthScore = hierarchicalFactory.getSystemHealthScore();
            
            return healthScore >= getFallbackThreshold();
            
        } catch (Exception e) {
            logger.error("Health check failed", e);
            return false;
        }
    }

    // ========== Spring生命周期 ==========

    /**
     * Spring初始化后回调
     */
    public void afterPropertiesSet() {
        try {
            if (isHierarchicalSortEnabled()) {
                ensureInitialized();
                logger.info("HierarchicalSortFactoryImpl initialized and ready");
            } else {
                logger.info("HierarchicalSortFactoryImpl initialized but hierarchical sort is disabled");
            }
        } catch (Exception e) {
            logger.error("Failed to initialize HierarchicalSortFactoryImpl", e);
            // 不抛出异常，允许系统继续启动，使用降级方案
        }
    }

    /**
     * Spring销毁前回调
     */
    public void destroy() {
        try {
            if (hierarchicalFactory != null) {
                hierarchicalFactory.destroy();
                logger.info("HierarchicalSortFactory destroyed");
            }
        } catch (Exception e) {
            logger.error("Failed to destroy HierarchicalSortFactory", e);
        }
    }
}
